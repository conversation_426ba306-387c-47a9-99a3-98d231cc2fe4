@Library('sparrow-libraries@master') _
variables = loadConstants()

import org.global.Cloudflare
import org.global.SecretManager

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': env.JOB_NAME.split('/').last(),
      'Branch': params.BranchName,
      'Environment': env.ENVIRONMENT,
      'Domain': "${variables.WEBSITE[env.JOB_NAME].domain}",
      'Started By': env.BUILD_USER,
      'channel': variables.WEBSITE.websiteDeploymentsChannel,
      'logs': "\n<${env.BUILD_URL}|Click here for more details>"
    ]   
  }
}

def purgeCache() {
  cloudflare = new Cloudflare(this, variables.CLOUDFLARE.DOMAINS["${variables.WEBSITE[env.JOB_NAME].domain}"]["zoneId"], CLOUDFLARE_API_TOKEN)
  cloudflare.purgeCache("hosts", [variables.WEBSITE[env.JOB_NAME].domain])
}

properties([
  buildDiscarder(
    logRotator(
      artifactDaysToKeepStr: '',
      artifactNumToKeepStr: '', 
      daysToKeepStr: '30', 
      numToKeepStr: '15'
    )
  ),
  parameters([ 
    string(
      name: 'BranchName', 
      defaultValue: "master", 
      description: "Enter the branch to build and deploy"
    )
  ]),
  disableConcurrentBuilds(
    abortPrevious: true
  )
])

pipeline {
  agent { label "ArmDockerBuilderFleet" }

  environment {
    ARGOCD_PASSWORD = credentials('SparrowdeskArgoPassword')
    CLOUDFLARE_API_TOKEN = credentials("CommonCloudflareToken")
  }

  stages {
    stage("GIT_CLONE") {
      steps {
        script {
          setNotificationParams()
          
          notify( "STARTED", notificationParams, variables )

          currentBuild.displayName = "Triggered: #${currentBuild.number}"
          currentBuild.description = "Deploying: ${params.BranchName} to Env: ${env.ENVIRONMENT}"
          
          dir('website') {
            gitUtils.cloneRepo( "${params.BranchName}", "${variables.GIT.websiteRepo}", "${variables.BITBUCKET_CREDENTIAL_ID}" )
          }

          dir('config') {
            gitUtils.cloneRepo( "main",  "${variables.CONFIG_REPO}", "${variables.BITBUCKET_CREDENTIAL_ID}" )
          }
        }
      }
    }

    stage("DOCKER_BUILD") {
      steps {
        script {
          dir("website") {

            currentBuild.displayName = "Building Docker: ${params.BranchName}: ${currentBuild.number}"
            currentBuild.description = "Building Docker - Staging Env: ${env.ENVIRONMENT} and deploying: ${params.BranchName}"

            def commitHash = gitUtils.getCommitHash(true)
            ecrBuildId = sh (script: "echo '${params.BranchName.replaceAll('/', '_')}_${commitHash}_${currentBuild.number}' | tr -d '\n'", returnStdout: true).trim()
            echo "Generated ECR Build ID: ${ecrBuildId}"

            echo "Copying the environment file"
            sh "cp ${env.WORKSPACE}/config/${variables.WEBSITE[env.JOB_NAME].envFile} .env"

            withAWS(region: variables.DEFAULT_REGION, roleAccount: variables.ACCOUNT_CONFIG.accountId, role: variables.ACCOUNT_CONFIG.roleArn) {
              echo "Fetching the secrets from AWS Secrets Manager"
              sm = new SecretManager(this)
              def secretsText = sm.fetchSecrets(variables.WEBSITE[env.JOB_NAME].secretName)

              def secrets = readJSON(text: secretsText)
              secrets.each { key, value ->
                sh "echo \"${key}=\"${value}\"\" >> .env"
              }
            }

            echo "Building the docker Image"
            dockerUtils.build("./Dockerfile", variables.ECR_REGISTRY[variables.DEFAULT_REGION] + "/" + variables.WEBSITE[env.JOB_NAME].ecrRepo, ['latest', ecrBuildId ] )
            echo "Docker Image Built Successfully"
          }
        }
      }
    }

    stage("UPLOAD_IMAGE_ECR") {
      steps {
        script {
          currentBuild.displayName = "Uploading ECR: ${params.BranchName}: ${currentBuild.number}"
          currentBuild.description = "Uploading ECR Staging Env: ${env.ENVIRONMENT} and deploying: ${params.BranchName}"

          echo "Authenticating to ECR"
          dockerUtils.authenticate(variables.ECR_REGISTRY[variables.DEFAULT_REGION] , variables.DEFAULT_REGION, variables.ACCOUNT_CONFIG )

          echo "Pushing Docker Image to ECR"
          dockerUtils.push(variables.ECR_REGISTRY[variables.DEFAULT_REGION]+ "/" + variables.WEBSITE[env.JOB_NAME].ecrRepo, ['latest', ecrBuildId ])
          echo "Docker Image Pushed to ECR Successfully"
          
        }
      }
    }

    stage("DEPLOYMENT_UPDATE") {
      steps {
        script {
          currentBuild.displayName = "Sync Config: ${params.BranchName}: ${currentBuild.number}"
          currentBuild.description = "Sync Config Staging Env: ${env.ENVIRONMENT} and deploying: ${params.BranchName}"

          dir('eks') {
            gitUtils.cloneRepo( "master",  "${variables.K8S_MANIFESTS.repoUrl}", "${variables.BITBUCKET_CREDENTIAL_ID}")
          }

          def kustomizePath = "${env.WORKSPACE}/eks/${variables.WEBSITE[env.JOB_NAME].kustomizePath}"

          def kustomizeData = readYaml(file: kustomizePath)
          kustomizeData.images[0].newTag = ecrBuildId
          writeYaml(file: kustomizePath, data: kustomizeData, overwrite: true)
        }
      }
    }

    stage("GIT_SYNC") {
      steps {
        script {
          currentBuild.displayName = "Syncing Git: ${params.BranchName}: ${currentBuild.number}"
          currentBuild.description = "Syncing Git Staging Env: ${env.ENVIRONMENT} and deploying: ${params.BranchName}"
          
          dir("eks") {
            sshagent(credentials: [variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush( "master", "Updated to Deployment Image: ${ecrBuildId}")
            }
          }
        }
      }
    }
    stage("ARGOCD_DEPLOYMENT") {
      steps {
        script {
          currentBuild.displayName = "Deploying: ${params.BranchName}: ${currentBuild.number}"
          currentBuild.description = "Deploying Staging Env: ${env.ENVIRONMENT} and deploying: ${params.BranchName}"
          
          argocd(
            action: variables.ARGOCD.ACTIONS.deploy,
            argoCdUsername: variables.ARGOCD.userName,
            argoCdPassword: env.ARGOCD_PASSWORD,
            argoCdAppName: variables.WEBSITE[env.JOB_NAME].argocdAppName,
            argoCdDomain: variables.ARGOCD.domain
          )
        }
      }
      post {
        failure {
          script {
            purgeCache()
          }
        }
      }
    }
    stage("CACHE_PURGE") {
      steps {
        script {
          purgeCache()

          echo "Waiting for the Cache to be purged"
          sh "sleep 30"
        }
      }
    }
  }

  post {
    always {
      script {
        cleanWs()
        notify( currentBuild.result, notificationParams, variables )
    
        currentBuild.displayName = "${currentBuild.result}: #${currentBuild.number}"
        currentBuild.description = "Deployment ${currentBuild.result} of Branch: ${params.BranchName}"
      }
    }
  }
}