@Library('sparrow-libraries@master') _ // Adding master here until the changelog pipeline is fixed.
variables = loadConstants()

import org.global.ParameterStore

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} container resource update",
      'Region': params.region,
      'Layer': params.layer,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice (name: 'region', choices: variables.REGION_NAMES, description: 'Specify which region to update the container resources'),
    choice (name: 'layer', choices: variables.LAYERS.application + variables.LAYERS.worker, description: 'Specify which layer to update the container resources'),
    string (name: 'reason', defaultValue: 'No reason provided', description: 'The reason for updating the resources')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('INITIALIZE_PIPELINE') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service     = "App-v1"
            serviceVars = variables[service]
          } else {
            service     = serviceIdentifier[1]
            serviceVars = variables[service]['backend']
          }

          region     = variables.DATA_CENTER_REGION_MAP[params.region]
          releaseEnv = variables.REGION_APP_MAP[region] // used only for staging

          if (!serviceVars.dcLayerMap[region]) {
            currentBuild.description = "${params.region} not present for service - ${service}"
            error "[-] Region - ${params.region} not present for service - ${service}"
          }

          if (
            serviceVars.dcLayerMap[region].workerLayers.contains(params.layer) ||
            serviceVars.dcLayerMap[region].appLayers.contains(params.layer)
          ) {
            currentBuild.displayName = "${params.region}"
            currentBuild.description = "${params.layer} - ${params.reason}"

            appName = variables.REGION_APP_MAP[region]
          } else {
            error "[-] Layer - ${params.layer} not present for service - ${service} in region - ${params.region}"
          }

          setNotificationParams()
          notify('STARTED', notificationParams, variables)
        }
      }
    }

    stage('GET_DEPLOYMENT') {
      steps {
        script {
          dir (variables.KUBERNETES_REPO.folder) {
            gitUtils.cloneRepo(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def yamlBasePath = env.ENVIRONMENT == 'staging' ? "${variables.KUBERNETES_REPO.folder}/kubectl/preproduction/${region}/" : "${variables.KUBERNETES_REPO.folder}/app-v1/kubectl/${region}/"
          def yamlFolder   = serviceVars.service == 'app-v1' ? "blue-green-templates/" : "${serviceVars.shortName}/"

          if (serviceVars.dcLayerMap[region].workerLayers.contains(params.layer)) {
            fileName = "worker.yaml"
          } else if (serviceVars.dcLayerMap[region].appLayers.contains(params.layer)) {
            fileName = "app.yaml"
          }

          def yamlFile = "${yamlBasePath}${yamlFolder}${fileName}"
          def deploymentYaml = readYaml file: yamlFile

          def deploymentName = serviceVars.service == 'app-v1' ? "${appName}-${params.layer}" : "${appName}-${serviceVars.shortName}-${params.layer}"
          if (deploymentYaml instanceof Map) {
            if (deploymentYaml.spec?.template?.metadata?.labels?.app == deploymentName) {
              deployment = deploymentYaml
            } else {
              error "No deployment found with app name: ${deploymentName}"
            }
          } else {
            def matchingDeployment = deploymentYaml.findAll { deployment ->
              deployment.spec?.template?.metadata?.labels?.app == deploymentName
            }

            if (matchingDeployment.isEmpty()) {
              error "No deployment found with app name: ${deploymentName}"
            } else {
              deployment = matchingDeployment[0]
            }
          }
        }
      }
    }

    stage('APPLY_DEPLOYMENT') {
      steps {
        script {
          def deploymentParams = [:]
          if (serviceVars.service == 'app-v1') {
            def parameterBase    = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}" : "/${region}"
            def requiredParams   = [ "deployment", "image", "app" ]
            def parameterStore   = new ParameterStore(this)
            for (def param in requiredParams) {
              def parameterPath = "${parameterBase}/${param}/green"
              deploymentParams[param] = parameterStore.getParameter(parameterPath)
            }
          }

          writeYaml file: "deployment-to-update.yaml", data: deployment, overwrite: true
          sh "STACK=${deploymentParams.deployment} TAG=${deploymentParams.app} IMAGE_TAG=${deploymentParams.image} envsubst < deployment-to-update.yaml > deployment-to-apply.yaml"
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          k8s.apply("deployment-to-apply.yaml")
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
