@Library('sparrow-libraries@master') _ // Adding master here until the changelog pipeline is fixed.
variables = loadConstants()

import org.global.ParameterStore
import org.global.MathUtils
import org.global.S3

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${env.JOB_NAME} ${deployment} deployment",
      'DC': env.JOB_BASE_NAME,
      'Release branch': params.releasebranch,
      'Deployment tag': params.deploymenttag,
      'Started By': env.BUILD_USER,
      'channel': params.releasebranch.replaceAll('/', '-')
    ]
  }
}

def runMigrations(migrationType) {
  def jobYaml = env.ENVIRONMENT == 'staging'
              ? "${variables.KUBERNETES_REPO.folder}/kubectl/preproduction/${region}/${microServiceVars.shortName}/migrations.yaml"
              : "${variables.KUBERNETES_REPO.folder}/app-v1/kubectl/${region}/${microServiceVars.shortName}/migrations.yaml"

  k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
  createAndApplyConfigMap(
    cmName: "${microServiceVars.namespace}-${microServiceVars.shortName}-migration-config",
    namespace: microServiceVars.namespace,
    data: [
      "${env.ENVIRONMENT}.json": readFile('temp-config.json')
    ]
  )

  def jobSelector = [ "migration_code": "${migrationType}-${microServiceVars.shortName}-migration-${env.BUILD_NUMBER}" ]
  def environmentVars = [ BUILD_NUMBER: env.BUILD_NUMBER, MIGRATION_TYPE: migrationType ]
  dir (variables.KUBERNETES_REPO.folder) {
    gitUtils.cloneRepo(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
  }

  runScriptAsJob (
    filePath: "${env.WORKSPACE}/${jobYaml}",
    selector: jobSelector,
    namespace: microServiceVars.namespace,
    log: true,
    logFile: "${migrationType}.txt",
    environment: environmentVars
  )

  def s3Path = "migrationlogs/${env.JOB_NAME}/${region}/${params.deploymenttag}/${migrationType}.txt"
  new S3(this).uploadFile("./${migrationType}.txt", variables.ARTIFACTS_BUCKET, s3Path)
  def s3Uri  = "s3://${variables.ARTIFACTS_BUCKET}/${s3Path}"

  def resultsMap = [
    logUrl: sh (returnStdout: true, script: "aws s3 presign ${s3Uri} --region ${variables.DEFAULT_REGION} --expires-in 7200").trim(),
    status: sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'RUNNING_MIGRATION_COMMAND_FAILED_WITH_ERRORS' && exit 1 || exit 0"),
    postMigrationExists: sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'POST_MIGRATION_REQUIRED' && exit 1 || exit 0")
  ]

  return resultsMap
}

CURRENT_STAGE = 0
STAGE_COUNT   = 6

def getJobParams() {
  return [
    string(name: 'releasebranch', description: 'Release branch that was merged.'),
    string(name: 'deploymenttag', description: 'Deployment Tag to be deployed.'),
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label 'DockerBuilderFleet'
  }

  tools {
    nodejs 'Node-v18.20.2'
  }

  environment {
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          sh "jq --version"

          def actualJobName = env.JOB_NAME
          env.JOB_NAME      = env.JOB_NAME.split('/')[1]
          deployment        = 'backend'

          region                      = variables.DATA_CENTER_REGION_MAP[env.JOB_BASE_NAME]
          releaseEnv                  = variables.REGION_APP_MAP[region] // This is for staging - need to find workaround.
          microServiceVars            = variables[env.JOB_NAME][deployment]
          lastSuccessfulBuildDuration = getJobLastSuccessfulDuration(actualJobName)

          progressNotificationMap = [percentage: CURRENT_STAGE, etc: (lastSuccessfulBuildDuration - currentBuild.duration), slackBotToken: env.SLACK_TOKEN, channelId: variables.DEPLOYMENTS_CHANNEL_ID, messageType: 'DEPLOYMENT_STARTED', application: "${env.JOB_NAME} ${deployment}", dc: env.JOB_BASE_NAME]
          messageTimestamp = sendProgressMessage(progressNotificationMap)
          progressNotificationMap.messageTimestamp = messageTimestamp

          dir(microServiceVars.folderName) {
            gitUtils.cloneRepo(params.deploymenttag, microServiceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)

            def parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/version/green" : "/${region}/version/green"  // TODO: remove this condition once staging is updated

            version           = new ParameterStore(this).getParameter(parameterPath)
            packageJsonHash   = ifInstallNodeModules("package.json", variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}/") // not used right now, will be putting to use later.
          }

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "${params.releasebranch}"
          postMigrationExists      = true

          setNotificationParams()

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          dir (variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def configFilePath = "${variables.CONFIG_REPO.folder}/${env.JOB_BASE_NAME}/${microServiceVars.shortName}/${env.ENVIRONMENT}.json"

          sh "jq '.version |= \"${version}\"' ${configFilePath} > ./temp-config.json"

          dir(microServiceVars.folderName) {
            sh "mkdir -p db/credentials/cassandra/"
            sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO.folder}/${env.JOB_BASE_NAME}/files/cassandra/* db/credentials/cassandra/"
            sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO.folder}/${env.JOB_BASE_NAME}/files/google/* config/"
            def dockerRegistry = variables.APP_DOCKER_REGISTRY[region]
            def repository     = env.ENVIRONMENT == 'staging' ? "${releaseEnv}-${microServiceVars.shortName}" : microServiceVars.service // To be removed
            def dockerRepo     = dockerRegistry + "/" + repository
            def tags           = [ 'latest', "${env.BUILD_NUMBER}" ]

            dockerUtils.authenticate(variables.APP_DOCKER_REGISTRY[variables.DEFAULT_REGION], variables.DEFAULT_REGION)
            dockerUtils.build("Dockerfiles/${env.ENVIRONMENT}", dockerRepo, tags)
            dockerUtils.authenticate(dockerRegistry, region)
            dockerUtils.push(dockerRepo, tags)
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('RUN_PRE_MIGRATIONS') {
      when {
        expression { microServiceVars['migrationsEnabled'] }
      }

      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval for pre migrations - ${env.JOB_NAME} ${deployment}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>\n\nPlease note that this is temporary, until migrations check is implemented.\nPlease click on proceed even if migrations are not present in the release."
          notify('APPROVAL', notificationParams, variables)
          input (message: "Proceed to run pre migrations?")

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} ${deployment} deployment"

          def migrationResult = runMigrations("PRE")
          if (migrationResult['status'] != 0) {
            error "PRE Migrations failed!"
          }

          postMigrationExists = migrationResult['postMigrationExists']

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('UPDATE_CONFIG') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval to proceed with the deployment - ${env.JOB_NAME} ${deployment}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>"
          notify('APPROVAL', notificationParams, variables)

          input (message: 'Are you sure to proceed with the deployment ?')

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} ${deployment} deployment"
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region) // In case migrations doesn't exist

          createAndApplyConfigMap (
            cmName: microServiceVars.namespace + '-' + microServiceVars.shortName + '-config',
            namespace: microServiceVars.namespace,
            data: [
              "${env.ENVIRONMENT}.json": readFile('temp-config.json')
            ]
          )

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('DEPLOY') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          def deployments  = []
          def appLayers    = microServiceVars.dcLayerMap[region]?.appLayers
          def workerLayers = microServiceVars.dcLayerMap[region]?.workerLayers

          if (workerLayers) {
            workerLayers.each { layer ->
              def deploymentName = "${variables.REGION_APP_MAP[region]}-${microServiceVars.shortName}-${layer}"
              deployments.add(deploymentName)
            }
          }

          if (appLayers) {
            appLayers.each { layer ->
              def deploymentName = "${variables.REGION_APP_MAP[region]}-${microServiceVars.shortName}-${layer}"
              deployments.add(deploymentName)
            }
          }

          for (def deployment in deployments) {
            k8s.restartDeployment(deployment, microServiceVars.namespace)
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('RUN_POST_MIGRATIONS') {
      when {
        expression { microServiceVars.migrationsEnabled && postMigrationExists }
      }

      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval for post migrations - ${env.JOB_NAME} ${deployment}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>"
          notify('APPROVAL', notificationParams, variables)
          input (message: "Proceed to run the post migrations?")

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} ${deployment} deployment"

          def migrationResult = runMigrations("POST")
          if (migrationResult['status'] != 0) {
            error "POST migrations failed"
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }
  }

  post {
    success {
      script {
        progressNotificationMap.percentage = 100
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_SUCCESSFUL'
        sendProgressMessage(progressNotificationMap)
        // notify('SUCCESS', notificationParams, variables)
        cleanWs()
      }
    }

    failure {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_FAILED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('FAILED', notificationParams, variables)
        cleanWs()
      }
    }

    aborted {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_ABORTED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('ABORTED', notificationParams, variables)
        cleanWs()
      }
    }
  }
}
