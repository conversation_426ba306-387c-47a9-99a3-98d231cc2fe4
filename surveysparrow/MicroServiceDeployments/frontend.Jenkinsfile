@Library('sparrow-libraries@v0.3.2') _
variables = loadConstants()

import org.global.ParameterStore
import org.global.CloudAuthManager
import org.global.MathUtils
import org.global.S3

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${env.JOB_NAME} ${deployment} deployment",
      'DC': env.JOB_BASE_NAME,
      'Release branch': params.releasebranch,
      'Deployment tag': params.deploymenttag,
      'Started By': env.BUILD_USER,
      'channel': params.releasebranch.replaceAll('/', '-')
    ]
  }
}

CURRENT_STAGE = 0
STAGE_COUNT   = 4

def getJobParams() {
  return [
    string(name: 'releasebranch', description: 'Release branch that was merged.'),
    string(name: 'deploymenttag', description: 'Deployment Tag to be deployed.'),
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label 'DockerBuilderFleet'
  }

  tools {
    nodejs 'Node-v18.20.2'
  }

  environment {
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          sh "jq --version"

          def actualJobName = env.JOB_NAME
          env.JOB_NAME      = env.JOB_NAME.split('/')[1]
          deployment        = 'frontend'

          region                      = variables.DATA_CENTER_REGION_MAP[env.JOB_BASE_NAME]
          releaseEnv                  = variables.REGION_APP_MAP[region] // This is for staging - need to find workaround.
          microServiceVars            = variables[env.JOB_NAME][deployment]
          lastSuccessfulBuildDuration = getJobLastSuccessfulDuration(actualJobName)

          progressNotificationMap = [percentage: CURRENT_STAGE, etc: (lastSuccessfulBuildDuration - currentBuild.duration), slackBotToken: env.SLACK_TOKEN, channelId: variables.DEPLOYMENTS_CHANNEL_ID, messageType: 'DEPLOYMENT_STARTED', application: "${env.JOB_NAME} ${deployment}", dc: env.JOB_BASE_NAME]
          messageTimestamp = sendProgressMessage(progressNotificationMap)
          progressNotificationMap.messageTimestamp = messageTimestamp

          dir(microServiceVars.folderName) {
            gitUtils.cloneRepo(params.deploymenttag, microServiceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)

            parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/${env.JOB_NAME}/version" : "/${region}/${env.JOB_NAME}/version"  // TODO: remove this condition once staging is updated
            def previousVersion = new ParameterStore(this).getParameter(parameterPath)

            def commit = sh returnStdout: true, script: "git rev-parse HEAD | tr -d '\n'"
            version    = sh returnStdout: true, script: "echo ${commit}${params.releasebranch} | md5sum - | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"

            if (previousVersion == version) {
              echo "[-] No commit or code changes made. Aborting the deployment. Dist version is same."
            }

            packageJsonHash = ifInstallNodeModules("package.json", variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}/") // not used right now, will be putting to use later.
          }

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "${params.releasebranch}"

          setNotificationParams()

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          dir (variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def configFilePath = "${variables.CONFIG_REPO.folder}/${env.JOB_BASE_NAME}/${microServiceVars.backendDeployment}/${env.ENVIRONMENT}.json"

          sh "jq '.version |= \"${version}\"' ${configFilePath} > ./temp-config.json"

          dir(microServiceVars.folderName) {
            new S3(this).downloadFile("node_modules/${env.JOB_NAME}/${packageJsonHash}/node_modules.tar.gz", variables.ARTIFACTS_BUCKET)
            sh "tar -xzf node_modules.tar.gz --overwrite --no-same-permissions --no-same-owner"
            sh "cp ../temp-config.json ./config/${env.ENVIRONMENT}.json"
            sh "NODE_ENV=${env.ENVIRONMENT} npm run prod"
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage ('UPDATE_CONFIGMAP') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval to proceed with the deployment - ${env.JOB_NAME} ${deployment}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>"
          notify('APPROVAL', notificationParams, variables)

          input (message: 'Are you sure to proceed with the deployment ?')

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} ${deployment} deployment"

          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          createAndApplyConfigMap (
            cmName: microServiceVars.namespace + '-' + microServiceVars.cmName,
            namespace: microServiceVars.namespace,
            data: [
              "${microServiceVars.cmKey}": version
            ]
          )

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('VALIDATE_DEPLOYMENT') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          // Needs to be updated for production
          def prismEndpoint = variables.PRISM.DOMAINS[region]
          def prismAPIPath  = "${prismEndpoint}${microServiceVars.validationPath}"

          def retries = 3
          def prismResponse = ""
          def jsonResponse  = [:]
          def mismatch_count = 0

          while (retries > 0) {
            prismResponse = sh (returnStdout: true, script: "curl -s -X POST ${prismAPIPath} -H '${variables.PRISM.HEADER}: ${microServiceVars.dcPrismToken[region]}' -H 'Content-Type: application/json' --data {}").trim()
            echo "${prismResponse}"
            jsonResponse  = readJSON ( text: prismResponse )

            if (jsonResponse['error_count'] > 0 && jsonResponse['error_rate'] > 0) {
              sleep 10
              retries -= 1
            } else {
              break
            }
          }

          def podResponses = jsonResponse['pods_responses']
          if (jsonResponse['error_count'] != 0 && jsonResponse['error_rate'] > 70) {
            k8s.restartDeployment(variables.REGION_APP_MAP[region] + "-" + microServiceVars.backendDeployment + "-application", microServiceVars.namespace)
          } else {
            for (def pod in podResponses.keySet()) {
              if( podResponses[pod]["error"] ||
                  (podResponses[pod]['data'][microServiceVars.versionKey] != version)
                ) {
                k8s.delete('pod', pod, microServiceVars.namespace)
              }
            }
          }

          new ParameterStore(this).putParameter(parameterPath, version)

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }
  }

  post {
    success {
      script {
        progressNotificationMap.percentage = 100
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_SUCCESSFUL'
        sendProgressMessage(progressNotificationMap)
        // notify('SUCCESS', notificationParams, variables)
        cleanWs()
      }
    }

    failure {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_FAILED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('FAILED', notificationParams, variables)
        cleanWs()
      }
    }

    aborted {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_ABORTED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('ABORTED', notificationParams, variables)
        cleanWs()
      }
    }
  }
}
