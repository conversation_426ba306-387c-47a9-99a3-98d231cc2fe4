@Library('sparrow-libraries@v0.2.2') _
variables = loadConstants()

properties([
  disableConcurrentBuilds(),
  buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
  pipelineTriggers([
    GenericTrigger(
      causeString: 'PR to master merged.',
      genericVariables: [
        [ key: 'prId', value: '$.pullrequest.id' ],
        [ key: 'sourceBranch', value: '$.pullrequest.source.branch.name' ],
        [ key: 'targetBranch', value: '$.pullrequest.destination.branch.name' ],
        [ key: 'repoName', value: '$.repository.name' ],
        [ key: 'commitId', value: '$.pullrequest.source.commit.hash' ]
      ],
      regexpFilterExpression: '',
      regexpFilterText: '$targetBranch',
      token: 'CodeQualityChecksToken',
      tokenCredentialId: '',
      printContributedVariables: true
    )
  ])
])

def BITBUCKET_TO_JENKINS_STATUS = [
  "SUCCESS": "SUCCESSFUL",
  "FAILURE": "FAILED",
  "ABORTED": "STOPPED"
]

List<String> runConftest(changedFiles) {
  def kustomizeDirs   = [] as Set
  def staticYamlFiles = []
  def failedDirs      = []

  // Install conftest
  sh '''
    if ! rpm -q conftest-0.60.0-1.aarch64 > /dev/null 2>&1; then
      sudo rpm -Uvh https://github.com/open-policy-agent/conftest/releases/download/v0.60.0/conftest_0.60.0_linux_arm64.rpm
    fi
  '''

  changedFiles.findAll { !it.contains('base/') } // Run kustomize only for non-base directories
    .each { file ->
      def dir = file.substring(0, file.lastIndexOf('/'))
      def kustomizePath = "${dir}/kustomization.yaml"

      if (fileExists(kustomizePath)) {
        kustomizeDirs << dir
      } else {
        staticYamlFiles << file
      }
    }

  sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
    kustomizeDirs.each { dir ->
      def buildResult = sh script: "kustomize build ${dir} | conftest test --no-color - -p ./policies", returnStatus: true
      if (buildResult != 0) {
        failedDirs.add(dir)
      }
    }

    staticYamlFiles.each { file ->
      def buildResult = sh script: "cat ${file} | conftest test --no-color - -p ./policies", returnStatus: true
      if (buildResult != 0) {
        failedDirs.add(file)
      }
    }
  }

  return failedDirs
}

Integer runYamllint(changedFiles) {
  sh "pip3 install yamllint"
  def yamllintStatus = sh script: "yamllint -c .yamllint.yml ${changedFiles.join(' ')}", returnStatus: true
  return yamllintStatus == 0 ? 0 : 1
}

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  environment {
    BB_TOKEN = credentials('BitBucketOAuthToken')
  }

  stages {
    stage ('INITIALIZE') {
      steps {
        script {
          def devOpsRepos = [ "SHARED_LIBRARY", "K8S_MANIFESTS", "PIPELINES", "TOFU_MODULES", "INFRACONFIG" ]
          repoKey         = ""
          def repoVariableKey = devOpsRepos.find { key ->
            def repo = variables.get(key)
            repoKey  = key
            return repo?.folder == repoName
          }

          Map linterNames = [
            "sparrow-k8s-manifests": "Kubernetes Lint",
            "sparrow-jenkins-shared-library": "Groovy Lint",
            "sparrow-pipeline": "Jenkinsfile Lint"
          ]

          linterName = linterNames[repoName]
          repoVars   = variables.get(repoVariableKey)
          loadResourceFile('global/bitbuckethelper.py')
          sh(script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${repoName} build-status-update -k 'code-quality-checks' -n '${linterName}' -s 'INPROGRESS' -d 'Linting is in progress for PR-${prId}.' -u '${currentBuild.absoluteUrl}console' -c '${commitId}'")

          def diffStat = sh(script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${repoName} get-diff -p ${prId}", returnStdout: true).trim()
          diffJson = readJSON(text: diffStat)
        }
      }
    }

    stage('KUBERNETES_MANIFESTS_VALIDATION') {
      when {
        expression {
          return repoName == variables.K8S_MANIFESTS.folder
        }
      }

      steps {
        script {
          changedFiles = diffJson.new.findAll { it.endsWith('.yaml') || it.endsWith('.yml') }

          if (changedFiles.size() > 0) {
            dir (repoVars.folder) {
              gitUtils.cloneRepo(sourceBranch, repoVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
              List<String> failedDirs = runConftest(changedFiles)
              Integer yamllintStatus  = runYamllint(changedFiles)

              if (failedDirs.size() > 0) {
                echo "[-] OPA Tests failed for the following directories / files:\n${failedDirs.join('\n')}"
              }

              if (yamllintStatus != 0) {
                echo "[-] YAML Lint failed"
              }

              if (failedDirs.size() > 0 || yamllintStatus != 0) {
                error "[-] Code Quality Checks failed"
              }
            }
          } else {
            echo "[-] No YAML files changed"
          }
        }
      }
    }

    stage('JENKINSFILE_SHARED_LIBRARY_VALIDATION') {
      when {
        expression {
          return repoName == variables.SHARED_LIBRARY.folder || repoName == variables.PIPELINES.folder
        }
      }

      steps {
        script {
          if (repoName == variables.SHARED_LIBRARY.folder) {
            changedFiles = diffJson.new.findAll { it.endsWith('.groovy') }
          } else {
            changedFiles = diffJson.new.collect { it }
          }

          if (changedFiles.size() > 0) {
            dir (repoVars.folder) {
              gitUtils.cloneRepo(sourceBranch, repoVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
              def nodevar = tool 'Node-v20.18.0'
              withEnv(["PATH+NODE=${nodevar}/bin"]) {
                sh "npm install npm-groovy-lint"
                def groovyLintStatus = sh script: "npx npm-groovy-lint -c .groovylintrc.json --failon error ${changedFiles.join(' ')}", returnStatus: true
                if (groovyLintStatus != 0) {
                  error "[-] Groovy Lint failed. You can fix the errors by running `npx npm-groovy-lint -c .groovylintrc.json --fix`"
                }
              }
            }
          } else {
            echo "[-] No Groovy files / Jenkinsfiles changed"
          }
        }
      }
    }
  }

  post {
    always {
      sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${repoName} build-status-update -k 'code-quality-checks' -n '${linterName}' -s '${BITBUCKET_TO_JENKINS_STATUS[currentBuild.result]}' -d '${linterName} ${BITBUCKET_TO_JENKINS_STATUS[currentBuild.result]} for PR-${prId}.' -u '${currentBuild.absoluteUrl}console' -c '${commitId}'")
      cleanWs()
    }
  }
}
