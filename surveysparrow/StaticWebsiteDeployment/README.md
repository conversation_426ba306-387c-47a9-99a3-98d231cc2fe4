# Cloudflare Pages Deployment Pipeline

This Jenkins pipeline automates the process of building and deploying static websites to Cloudflare Pages for the Surveysparrow project. It is designed to work with Jenkins and leverages shared libraries and environment-specific configurations.

## Pipeline Overview

The pipeline performs the following steps:

1. **Initialize Pipeline**
   - Sets up branch information (from parameters or default branch).
   - Determines deployment URL based on branch (production domain for default branch, preview URL for feature branches).
   - Configures pipeline display information and notification parameters.

2. **Pull Repository**
   - Clones the website repository from Bitbucket using Git.
   - Uses the specified branch or defaults to the configured default branch.

3. **Install Dependencies**
   - Installs project dependencies using the configured package manager (`npm` or `yarn`).
   - Supports both npm and yarn package managers based on app configuration.

4. **Run Build**
   - Executes the build process with environment-specific variables.
   - Runs `npm run build` or `yarn run build` depending on package manager configuration.
   - Applies environment variables defined in the app configuration.

5. **Deploy to Cloudflare**
   - Installs Cloudflare Wrangler CLI tool.
   - Creates Cloudflare Pages project if it doesn't exist.
   - Deploys the built static files to Cloudflare Pages.
   - Handles both production and preview deployments.

6. **Purge CloudFlare Cache** (Optional)
   - Purges Cloudflare cache for the deployed domain.
   - Only runs when `cloudflareCacheEnabled` is set to `true` in app configuration.

7. **Post Deployment** (Optional)
   - Executes custom post-deployment commands if specified.
   - Runs when `postDeploymentCMD` is defined in app configuration.

## Parameters

### Preview Deployments
- **branchname**: (Preview jobs only) Specify the job name with `preview` to deploy other branches (defaults to 'master').

## Deployment URLs

- **Production**: `https://{envConfig.domain}` (for default branch)
- **Preview**: `https://{branch}.{envConfig.projectName}.pages.dev` (for feature branches) Make sure branch name is within 28 characters.

## Environment Requirements

- **Agent**: Runs on agents labeled according to `agentLabel` in config or defaults to `DockerBuilderFleet`, you can choose either `DockerBuilderFleet` or `ArmDockerBuilderFleet`.
- **Node.js**: Uses the version specified in `nodeVersion` in config or defaults to `Node-v20.18.3`.
- **Credentials**: Requires `CLOUDFLARE_API_TOKEN` for Cloudflare API access.

## Notifications

- Sends deployment notifications to the configured Slack channel specified in `slackChannel` in config.
- Includes information about:
  - Pipeline name and branch
  - Service name and deployment URL
  - User who triggered the deployment
  - Build status (STARTED, SUCCESS, FAILURE)

## Configuration

Applications are configured through the shared constants library in the `CLOUDFLARE.WEBSITES` map. Each app requires the following configuration:

### Configuration Example
```groovy
[
   'CLOUDFLARE': [
      'WEBSITES': [
         'WebsiteName': [ // Replace WebsiteName with your website's name
            'repoUrl': '*****************:surveysparrow/repo-name.git', // ssh url only
            'folderName': 'ssdocs', // you can set any name, it will be used for reference purpose
            'packageManager': 'yarn', // your package manager name.
            'buildDir': 'build', // Specify the folder name which will have generated build resources (It should have your root's index.html)
            'postDeploymentCMD': 'yarn run algolia-index-update', // (Optional), Post deployment command. for example, here we need to update algolia's index 
            'cloudflareCacheEnabled': true,
            "slackChannel": "staging-deployments", // specify the slack channel where you want to get 
            "env-name": [
                "domain": "docs-demo.salesparrow.com", // your expected domain url for your website
                "envs": [ // envs map
                    "BACKEND_URL": "https://developers.salesparrow-demo.com",  // Here, BACKEND_URL is variable name and https://developers.salesparrow-demo.com is value 
                    "REGION": "US", 
                ],
                "projectName": "ss-docs-salesparrow-demo"
            ],
         ]
      ]
   ]
]
```
### Pipeline Naming Rule

Base Name: The pipeline name should always start with the WebsiteName value given in config and it is case sensitive.

**Environment Suffix Rule:**

- If the pipeline is in staging Jenkins and the environment is not `staging`, then append `-env-name`.
- If the pipeline is in production Jenkins and the environment is not `production`, then append `-env-name`. Kindly refrain having multiple envs in production unless necessary.
- If the environment matches the Jenkins type (`staging` or `production`), then do not append anything.

Example:

`WebsiteName` = `EdithDocs`
`env-name` = `Typesparrow`
and we are in staging jenkins

Since env-name ≠ staging, the final pipeline name = EdithDocs-Typesparrow


## Error Handling

The pipeline will fail and send notifications if any of the following occur:
- Repository cloning fails
- Dependency installation fails (`npm install` or `yarn install`)
- Build process fails (`npm run build` or `yarn run build`)
- Cloudflare deployment fails
- Post-deployment command execution fails

## Timeouts

- **Repository Pull**: 5 minutes
- **Install Dependencies**: 10 minutes
- **Build Process**: 15 minutes
- **Cloudflare Deployment**: 20 minutes
- **Cache Purge**: 5 minutes

## Security

- Uses Jenkins credentials for:
  - Cloudflare API token (`CLOUDFLARE_API_TOKEN`)
  - Bitbucket repository access
- Supports secure environment variable injection
- Automatically cleans workspace after deployment

## Usage

### Production Deployment
1. Trigger the production pipeline job
2. The pipeline will automatically deploy the default branch to the production domain

### Preview Deployment
1. Trigger the preview pipeline job
2. Specify the `branchname` parameter for the branch you want to deploy
3. The pipeline will deploy to a preview URL: `https://{branch}.{projectName}.pages.dev`

### Job Naming Convention
- Production jobs: `{app-name}-{environment}`
- Preview jobs: Include "preview" in the job name for branch parameter support

## Monitoring

- Pipeline execution details are displayed in Jenkins build information
- Slack notifications provide real-time status updates
- Build history is retained for 5 days/5 builds
- Concurrent builds are disabled to prevent conflicts
