@Library('sparrow-libraries@master') _
variables = loadConstants()

import org.global.Cloudflare

jobNameTokens = env.JOB_BASE_NAME.split('-')
appName = jobNameTokens[0]
envName = jobNameTokens.size() > 1 ? jobNameTokens[1].split(' ')[0] : env.ENVIRONMENT
appConfig = variables.CLOUDFLARE.WEBSITES[appName]
envConfig = appConfig[envName]

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "Cloudflare Page Deployment",
      'Branch': branch,
      'Service Name': appName,
      'Started By': env.BUILD_USER,
      'channel': appConfig.slackChannel,
      'Deployed URL': url
    ]
  }
}

def setPipelineInfo() {
  wrap([$class: 'BuildUser']) {
    currentBuild.displayName = "${branch} | ${env.BUILD_NUMBER}"
    currentBuild.description = "Branch: ${branch} - Deployed URL: ${url} - Started By: ${env.BUILD_USER}"
  }
}

def genParameters() {
  def _parameters = []
  if(env.JOB_BASE_NAME.toLowerCase().contains('preview')){
    _parameters.add(string(
      name: 'branchname',
      description: 'Branch to deploy',
      defaultValue: 'master'
    ))
  }

  return _parameters
}

properties([
  disableConcurrentBuilds(),
  buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
  parameters(genParameters())
])

pipeline {
  agent { label appConfig.agentLabel ?: 'DockerBuilderFleet' }

  tools {
    nodejs appConfig.nodeVersion ?: 'Node-v20.18.3'
  }

  environment {
    CLOUDFLARE_API_TOKEN  = credentials("${variables.CLOUDFLARE.TOKEN_ID}")
  }

  stages {
    stage("Initialize Pipeline") {
      steps {
        script {
          branch = params.branchname ? params.branchname : variables.DEFAULT_BRANCH
          url = branch == variables.DEFAULT_BRANCH ? "https://${envConfig.domain}" : "https://${branch}.${envConfig.projectName}.pages.dev"
          setPipelineInfo()
          setNotificationParams()
          notify("STARTED", notificationParams, variables)
        }
      }
    }

    stage("Pull Repository") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      steps {
        script {
          dir("repository") {
            try {
              gitUtils.cloneRepoWithGit(branch, appConfig.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            }
            catch(Exception e) {
              echo "Error cloning repository: ${e}"
              throw e
            }
          }
          path = appConfig.subPath ? "repository/${appConfig.subPath}" : "repository"
        }
      }
    }

    stage("Install Dependencies") {
      options {
        timeout(time: 10, unit: 'MINUTES')
      }
      steps {
        script {
          def pkgMgr = ''
          dir(path) {
            if (fileExists("pnpm-lock.yaml")) {
              pkgMgr = 'pnpm'
              sh 'echo alias pnpm="corepack pnpm" >> ~/.bash_aliases'
              installCmd = 'pnpm install --frozen-lockfile'
              buildCmd = 'pnpm run build'
            } else if (fileExists("yarn.lock")) {
              pkgMgr = 'yarn'
              installCmd = 'yarn install --frozen-lockfile'
              buildCmd = 'yarn build'
            } else if (fileExists("package-lock.json")) {
              pkgMgr = 'npm'
              installCmd = 'npm ci'
              buildCmd = 'npm run build'
            } else if (fileExists("bun.lockb")) {
              pkgMgr = 'bun'
              installCmd = 'bun install --production'
              buildCmd = 'bun run build'
            } else {
              error "No known lock file found in ${path}"
            }
            echo "Using package manager: ${pkgMgr}"
          }

          if (pkgMgr != 'npm' && pkgMgr != 'bun') {
            sh "npm install -g corepack"
          }

          dir(path) {
            sh installCmd
          }
        }
      }
    }

    stage("Run Build") {
      options {
        timeout(time: 15, unit: 'MINUTES')
      }
      steps {
        script {
          dir(path) {
            envs = ""
            envConfig.envs.each { key, value ->
              envs += "${key}=\"${value}\" "
            }
            sh "${envs} ${buildCmd}"
          }
        }
      }
    }

    stage("Deploy to Cloudflare") {
      options {
        timeout(time: 20, unit: 'MINUTES')
      }
      steps {
        script {
          cloudflare = new Cloudflare(this, envConfig.zoneId, CLOUDFLARE_API_TOKEN, variables.CLOUDFLARE.ACCOUNT_ID)
          cloudflare.installWrangler()
          dir(path) {
            cloudflare.createPagesProjectIfNotExist(envConfig.projectName, variables.DEFAULT_BRANCH, envConfig.domain)
            cloudflare.deployPages(envConfig.projectName, appConfig.buildDir, branch)
          }
        }
      }
    }

    stage("Purge CloudFlare Cache") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      when {
        expression {
          appConfig.cloudflareCacheEnabled == true
        }
      }
      steps {
        script {
          cloudflare.purgeCache("hosts", [envConfig.domain])
        }
      }
    }

    stage("Post Deployment") {
      when {
        expression {
          appConfig.postDeploymentCMD != null
        }
      }
      steps {
        script {
          try {
            dir(path) {
              sh "${envs} ${appConfig.postDeploymentCMD}"
            } 
          }
          catch(Exception e) {
            error "Error running post deployment command: ${e}"
          }
        }
      }
    }
  }

  post {
    always {
      notify(currentBuild.currentResult, notificationParams, variables)
      cleanWs()
    }
  }
}