@Library('sparrow-libraries@v0.1.2') _
variables = loadConstants()

import org.global.Cloudflare

jobNameTokens = env.JOB_BASE_NAME.split('-')
appName = jobNameTokens[0]
envName = jobNameTokens.size() > 1 ? jobNameTokens[1] : env.ENVIRONMENT
appConfig = variables.CLOUDFLARE.WEBSITES[appName]
envConfig = appConfig[envName]

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "Cloudflare Page Deployment",
      'Branch': branch,
      'Service Name': appName,
      'Started By': env.BUILD_USER,
      'channel': appConfig.slackChannel,
      'Deployed URL': url
    ]
  }
}

def setPipelineInfo() {
  wrap([$class: 'BuildUser']) {
    currentBuild.displayName = "${branch} | ${env.BUILD_NUMBER}"
    currentBuild.description = "Branch: ${branch} - Deployed URL: ${url} - Started By: ${env.BUILD_USER}"
  }
}

def genParameters() {
  def _parameters = []
  if(env.JOB_BASE_NAME.toLowerCase().contains('preview')){
    _parameters.add(string(
      name: 'branchname',
      description: 'Branch to deploy',
      defaultValue: 'master'
    ))
  }

  return _parameters
}

properties([
  disableConcurrentBuilds(),
  buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
  parameters(genParameters())
])

pipeline {
  agent { label appConfig.agentLabel ?: 'DockerBuilderFleet' }

  environment {
    CLOUDFLARE_API_TOKEN  = credentials("${variables.CLOUDFLARE.TOKEN_ID}")
  }

  stages {
    stage("Initialize Pipeline") {
      steps {
        script {
          branch = params.branchname ? params.branchname : variables.DEFAULT_BRANCH
          url = branch == variables.DEFAULT_BRANCH ? "https://${envConfig.domain}" : "https://${branch}.${envConfig.projectName}.pages.dev"
          setPipelineInfo()
          setNotificationParams()
          notify("STARTED", notificationParams, variables)
        }
      }
    }

    stage("Pull Repository") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            try {
              gitUtils.cloneRepoWithGit(branch, appConfig.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            }
            catch(Exception e) {
              echo "Error cloning repository: ${e}"
              throw e
            }
          }
        }
      }
    }

    stage("Install Dependencies") {
      options {
        timeout(time: 10, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            if(appConfig.packageManager == 'npm') {
              sh "npm install"
            }
            else if(appConfig.packageManager == 'yarn') {
              sh "yarn install"
            }
          }
        }
      }
    }

    stage("Run Build") {
      options {
        timeout(time: 15, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            def envs = ""
            envConfig.envs.each { key, value ->
              envs += "${key}=\"${value}\" "
            }
            if(appConfig.packageManager == 'npm') {
              sh "${envs} npm run build"
            }
            else if(appConfig.packageManager == 'yarn') {
              sh "${envs} yarn run build"
            }
          }
        }
      }
    }

    stage("Deploy to Cloudflare") {
      options {
        timeout(time: 20, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            cloudflare = new Cloudflare(this, appConfig.zoneId, CLOUDFLARE_API_TOKEN, variables.CLOUDFLARE.ACCOUNT_ID)
            cloudflare.installWrangler()
            cloudflare.createPagesProjectIfNotExist(envConfig.projectName, variables.DEFAULT_BRANCH, envConfig.domain)
            cloudflare.deployPages(envConfig.projectName, appConfig.buildDir, branch)
          }
        }
      }
    }

    stage("Purge CloudFlare Cache") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      when {
        expression {
          appConfig.cloudflareCacheEnabled == true
        }
      }
      steps {
        script {
          cloudflare.purgeCache("hosts", [envConfig.domain])
        }
      }
    }
  }

  post {
    always {
      notify(currentBuild.currentResult, notificationParams, variables)
      cleanWs()
    }
  }
}