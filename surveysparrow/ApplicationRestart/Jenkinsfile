@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

import org.global.ParameterStore

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} application restart",
      'Region': params.region,
      'Layer': params.layer,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice(name: 'region', choices: variables.REGION_NAMES, description: 'Specify which region to restart the application'),
    choice(name: 'layer', choices: variables.LAYERS.application + variables.LAYERS.worker, description: 'Specify which layer to restart the application'),
    string(name: 'reason', description: 'Specify the reason to restart the application')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())


pipeline {
  agent {
    label "AlwaysOnFleet"
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else if (serviceIdentifier[0] == 'MicroServices') {
            service = serviceIdentifier[1]
            deployment  = 'backend'
            serviceVars = variables[service][deployment]
          }

          region     = variables.DATA_CENTER_REGION_MAP[params.region]
          releaseEnv = variables.REGION_APP_MAP[region] // used only for staging

          if (!serviceVars.dcLayerMap[region]) {
            currentBuild.description = "${params.region} not present for service - ${service}"
            error "[-] Region - ${params.region} not present for service - ${service}"
          }

          if (
            serviceVars.dcLayerMap[region].workerLayers.contains(params.layer) ||
            serviceVars.dcLayerMap[region].appLayers.contains(params.layer)
          ) {
            currentBuild.displayName = "${params.region}"
            currentBuild.description = "${params.layer} - ${params.reason}"
          } else {
            error "[-] Layer - ${params.layer} not present for service - ${service} in region - ${params.region}"
          }

          setNotificationParams()
          notify('STARTED', notificationParams, variables)
        }
      }
    }

    stage('EXECUTE_RESTART') {
      steps {
        script {
          def appName = variables.REGION_APP_MAP[region]

          if (serviceVars.service == 'app-v1') {
            def deploymentParameter = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/deployment/green" : "/${region}/deployment/green"
            def deploymentParam     = new ParameterStore(this).getParameter(deploymentParameter)

            appName = appName + '-' + params.layer + '-' + deploymentParam
          } else {
            appName = appName + '-' + serviceVars.shortName + '-' + params.layer
          }

          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          k8s.restartDeployment(appName, serviceVars.namespace)
          k8s.clearAuth()
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
