@Library('sparrow-libraries@master') _ // Adding master here until the changelog pipeline is fixed.
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} config update",
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  def deploymentParams = [];
  deploymentParams.add(
    activeChoice(
      choiceType: 'PT_CHECKBOX',
      description: 'Select the regions to update config',
      filterLength: 1,
      filterable: false,
      name: 'regions',
      randomName: 'choice-parameter-704386024516185',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: ''
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: variables.REGION_NAMES.toString()
        ]
      )
    )
  )

  if (env.JOB_BASE_NAME == 'ContainerResourceUpdate') {
    deploymentParams.add(
      choice(
        name: 'layer',
        description: 'Select the layer to update config',
        choices: variables.LAYERS.application + variables.LAYERS.worker
      )
    )
  } else if (env.JOB_BASE_NAME == 'WorkerQueueConfigUpdate') {
    deploymentParams.add(
      choice(
        name: 'layer',
        description: 'Select the layer to update config',
        choices: variables.LAYERS.worker
      )
    )
  }

  deploymentParams.add(
    string(
      name: 'reason',
      description: 'Please provide the reason for this config update'
    )
  )

  return deploymentParams
}

def getProperties() {
  def defaultProperties = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]

  defaultProperties.add(parameters(getJobParams()))
  return defaultProperties
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service      = "App-v1"
            serviceVars  = variables[service]
            jobToTrigger = "${service}/Operations/${env.JOB_BASE_NAME}"
          } else if (serviceIdentifier[0] == 'MicroServices') {
            service      = serviceIdentifier[1]
            serviceVars  = variables[service]['backend']
            jobToTrigger = "Microservices/${service}/backend/Operations/${env.JOB_BASE_NAME}"
          }

          setNotificationParams()
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${params.regions}"
          currentBuild.description = "${params.reason}"
        }
      }
    }

    stage('UPDATE_CONFIG') {
      parallel {
        stage('US-VIRGINIA') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('EU-FRANKFURT') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('AP-MUMBAI') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('ME-UAE') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('UK-LONDON') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('AU-SYDNEY') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }

        stage('CA-CANADA') {
          when {
            expression {
              params.regions.contains(env.STAGE_NAME) && serviceVars.dcLayerMap[variables.DATA_CENTER_REGION_MAP[env.STAGE_NAME]]
            }
          }

          steps {
            build job: jobToTrigger, parameters: [ string(name: 'region', value: env.STAGE_NAME), string(name: 'reason', value: "${params.reason}") ]
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
