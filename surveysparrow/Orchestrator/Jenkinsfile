@Library('sparrow-libraries@master') _ // Adding master here until the changelog pipeline is fixed.
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} ${deployment} deployment",
      'Release branch': params.releasebranch,
      'Deployment tag': GIT_TAG_FULL,
      'Started By': env.BUILD_USER,
      'channel': params.releasebranch.replaceAll('/', '-')
    ]
  }
}

def getJobParams() {
  def deploymentParams = [];

  deploymentParams.add(
    string (name: 'releasebranch', description: "Release branch to deploy")
  )

  if (env.JOB_NAME.split('/')[0] == "App-v1") {
    deploymentParams.add(
      booleanParam (name: 'compileassets', defaultValue: true, description: 'If compile assets ?')
    )
  }

  return deploymentParams
}

def getProperties() {
  def defaultProperties = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]

  defaultProperties.add(parameters(getJobParams()))
  return defaultProperties
}

properties(getProperties())

pipeline {
  agent {
    label "DockerBuilderFleet"
  }

  environment {
    JIRA_TOKEN = credentials('JiraAuthToken')
  }

  stages {
    stage('CREATE_DEPLOYMENT_TAG') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service     = "App-v1"
            deployment  = ''
            serviceVars = variables[service]
          }

          if (serviceIdentifier[0] == "MicroServices") {
            service     = serviceIdentifier[1]
            deployment  = serviceIdentifier[2]
            serviceVars = variables[service][deployment]
          }

          currentBuild.displayName = "#${params.releasebranch}"

          loadResourceFile('global/jirahelper.py')
          def issuekeys = sh(returnStdout: true, script: "RELEASE_BRANCH=${params.releasebranch} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} python3 jirahelper.py -k").trim()
          def issueKeysArr = "${issuekeys.replace('[','')}"
          issueKeysArr     = "${issueKeysArr.replace(']', '')}"
          issueList = []
          for(def issue in issueKeysArr.split(',')) {
            issueList.add(issue.replace("'", '').trim())
          }

          echo "[+] Merging ${params.releasebranch} to ${variables.PRODUCTION_RELEASE_BRANCH} in ${serviceVars.service} and creating a deployment tag."
          dir(serviceVars.folderName) {
            gitUtils.cloneRepoWithGit(params.releasebranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              sh "git submodule update --init --recursive"

              for (submodule in variables.SUBMODULES[serviceVars.folderName]) {
                dir (submodule) {
                  gitUtils.checkoutMergeAndPush(params.releasebranch, variables.PRODUCTION_RELEASE_BRANCH)
                  gitUtils.checkoutMergeAndPush(variables.PRODUCTION_RELEASE_BRANCH, variables.PRODUCTION_BRANCH)
                }
              }

              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                gitUtils.commitAndPush(params.releasebranch, 'Update submodule reference')
              }

              gitUtils.checkoutMergeAndPush(params.releasebranch, variables.PRODUCTION_RELEASE_BRANCH)
              gitUtils.checkoutMergeAndPush(variables.PRODUCTION_RELEASE_BRANCH, variables.PRODUCTION_BRANCH)

              gitUtils.setGitConfig()
              def GIT_TAG = env.ENVIRONMENT == 'staging' ? "preproduction-${params.releasebranch.split('/')[1]}" : "production-${new Date().format('dd-MM-yyyy')}"
              def GIT_TAG_COUNT = sh(script: "git tag --list '${GIT_TAG}-*' | wc -l | tr -d '[:space:]'", returnStdout: true).trim().toInteger() + 1
              GIT_TAG_FULL = "${GIT_TAG}-${GIT_TAG_COUNT}"
              sh """
              git tag ${GIT_TAG_FULL}
              git push origin ${GIT_TAG_FULL}
              """

              setNotificationParams()
              notify('STARTED', notificationParams, variables)
            }
          }

          if (env.ENVIRONMENT == 'staging') {
            dir (variables.CONFIG_REPO.folder) {
              gitUtils.cloneRepoWithGit(params.releasebranch, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
              sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
                gitUtils.checkoutMergeAndPush(params.releasebranch, variables.PRODUCTION_BRANCH)
              }
            }
          }
        }
      }
    }

    stage('DEPLOYMENT') {
      parallel {
        stage ('US-VIRGINIA') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('EU-FRANKFURT') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('AP-MUMBAI') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('ME-UAE') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('UK-LONDON') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('AU-SYDNEY') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }

        stage ('CA-CANADA') {
          when {
            expression { serviceVars.dcJobNamesMap[env.STAGE_NAME] != null }
          }

          steps {
            build job: serviceVars.dcJobNamesMap[env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), booleanParam(name: 'compileassets', value: params.compileassets)]
          }

          post {
            success {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }

            failure {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: env.ENVIRONMENT, issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'failed'
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        if (currentBuild.result == 'SUCCESS' && env.ENVIRONMENT == 'production') {
          notificationParams['pipeline'] = "<!channel> ${service} ${deployment} deployment"
        }

        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        currentBuild.displayName = "#${GIT_TAG_FULL} - ${params.releasebranch}"
      }
    }

    success {
      script {
        if (env.ENVIRONMENT == 'production') {
          sh "RELEASE_BRANCH=${params.releasebranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} APPLICATION=${service} python3 jirahelper.py -p"
          sh "RELEASE_BRANCH=${params.releasebranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} APPLICATION=${service} python3 jirahelper.py -r"
        }
        cleanWs()
      }
    }
  }
}
