@Library('sparrow-libraries@v0.2.2') _
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} ECS Deployment",
      'Environment': env.ENVIRONMENT,
      'Region': region,
      'Cluster': params.cluster,
      'Service': params.servicename,
      'Version': version,
      'Started By': env.BUILD_USER,
      'channel': releaseEnv
    ]
  }
}

def sendProgressMessage(progressMap) {
  progressMap.pipeline = "${service} ECS Deployment"
  progressMap.environment = env.ENVIRONMENT
  progressMap.region = region
  progressMap.version = version
  notify("PROGRESS", progressMap, variables)
}

def getJobParams() {
  return [
    string(name: 'releasebranch', description: 'Release branch to deploy from.', trim: true),
    string(name: 'deploymenttag', description: 'Deployment tag to be deployed.', trim: true),
    choice(name: 'cluster', choices: variables.ECS_CLUSTERS, description: 'ECS Cluster to deploy to'),
    string(name: 'servicename', description: 'ECS Service name to deploy', trim: true),
    choice(name: 'deploymenttype', choices: ['rolling', 'blue-green'], description: 'Deployment strategy'),
    booleanParam(name: 'skipbuild', defaultValue: false, description: 'Skip Docker build and use existing image'),
    booleanParam(name: 'autorollback', defaultValue: false, description: 'Automatically rollback on deployment failure'),
    string(name: 'description', defaultValue: '', description: 'Deployment description', trim: true)
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

STAGE_COUNT   = 8
CURRENT_STAGE = 0

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label "DockerBuilderFleet"
  }

  tools {
    nodejs "Node-v18.20.2"
  }

  environment {
    AWS_DEFAULT_REGION = "${variables.DEFAULT_REGION}"
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('INITIALIZE') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          service     = serviceIdentifier[1]
          serviceVars = variables[service]

          if (!serviceVars) {
            error "[-] Service ${service} configuration not found in variables"
          }

          region = variables.CLUSTER_REGION_MAP[params.cluster] ?: variables.DEFAULT_REGION
          releaseEnv = env.ENVIRONMENT == 'staging' ? 'preproduction' : region

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "${params.releasebranch} -> ${params.cluster}/${params.servicename}"

          setNotificationParams()
          notify("STARTED", notificationParams, variables)

          progressNotificationMap = [
            percentage: 0,
            etc: 0
          ]

          lastSuccessfulBuildDuration = getLastSuccessfulBuildDuration()

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          dir(serviceVars.folderName) {
            gitUtils.cloneRepo(params.deploymenttag, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            packageJsonHash = ifInstallNodeModules('package.json', variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}")
            def commitHash  = gitUtils.getCommitHash()
            buildTag        = "${commitHash}_${env.BUILD_NUMBER}"
          }

          dir (variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            configFilePath = "${releaseEnv}/${serviceVars.shortName}/${env.ENVIRONMENT}.json"

            sh """
              jq '.version |= \"${version}\"' ${configFilePath} > temp.json
              cp temp.json ../${serviceVars.folderName}/config/${env.ENVIRONMENT}.json
              mv temp.json ${configFilePath}
            """
          }

          version = new ParameterStore(this).getParameter("/${releaseEnv}/version")

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('DOCKER_BUILD') {
      when {
        not { params.skipbuild }
      }

      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          def ecrRegistry = variables.ECR_REGISTRY[region]
          def repository  = "${releaseEnv}-${serviceVars.shortName}"
          def ecrRepo     = "${ecrRegistry}/${repository}"
          def tags        = ['latest', buildTag, params.deploymenttag]

          dir(serviceVars.folderName) {
            sh "rm -rf node_modules/"
            
            // Authenticate with ECR
            sh "aws ecr get-login-password --region ${region} | docker login --username AWS --password-stdin ${ecrRegistry}"
            
            // Build Docker image
            dockerUtils.build("Dockerfiles/${env.ENVIRONMENT}", ecrRepo, tags)
            
            // Push to ECR
            dockerUtils.push(ecrRepo, tags)
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('UPDATE_TASK_DEFINITION') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          def ecrRegistry = variables.ECR_REGISTRY[region]
          def repository  = "${releaseEnv}-${serviceVars.shortName}"
          def imageUri    = "${ecrRegistry}/${repository}:${buildTag}"

          // Get current task definition
          def taskDefArn = sh(
            script: "aws ecs describe-services --cluster ${params.cluster} --services ${params.servicename} --region ${region} --query 'services[0].taskDefinition' --output text",
            returnStdout: true
          ).trim()

          def taskDefJson = sh(
            script: "aws ecs describe-task-definition --task-definition ${taskDefArn} --region ${region} --query 'taskDefinition'",
            returnStdout: true
          ).trim()

          // Update task definition with new image
          def updatedTaskDef = readJSON(text: taskDefJson)
          updatedTaskDef.containerDefinitions[0].image = imageUri
          
          // Remove fields that shouldn't be in registration
          updatedTaskDef.remove('taskDefinitionArn')
          updatedTaskDef.remove('revision')
          updatedTaskDef.remove('status')
          updatedTaskDef.remove('requiresAttributes')
          updatedTaskDef.remove('placementConstraints')
          updatedTaskDef.remove('compatibilities')
          updatedTaskDef.remove('registeredAt')
          updatedTaskDef.remove('registeredBy')

          writeJSON file: 'updated-task-definition.json', json: updatedTaskDef

          // Register new task definition
          newTaskDefArn = sh(
            script: "aws ecs register-task-definition --cli-input-json file://updated-task-definition.json --region ${region} --query 'taskDefinition.taskDefinitionArn' --output text",
            returnStdout: true
          ).trim()

          echo "[+] New task definition registered: ${newTaskDefArn}"

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('DEPLOY_TO_ECS') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          if (params.deploymenttype == 'blue-green') {
            // Blue-Green Deployment
            echo "[+] Starting Blue-Green deployment"

            // Update service with new task definition
            sh """
              aws ecs update-service \\
                --cluster ${params.cluster} \\
                --service ${params.servicename} \\
                --task-definition ${newTaskDefArn} \\
                --region ${region}
            """

            // Wait for deployment to complete
            echo "[+] Waiting for deployment to stabilize..."
            sh """
              aws ecs wait services-stable \\
                --cluster ${params.cluster} \\
                --services ${params.servicename} \\
                --region ${region}
            """

          } else {
            // Rolling Deployment
            echo "[+] Starting Rolling deployment"

            sh """
              aws ecs update-service \\
                --cluster ${params.cluster} \\
                --service ${params.servicename} \\
                --task-definition ${newTaskDefArn} \\
                --deployment-configuration maximumPercent=200,minimumHealthyPercent=50 \\
                --region ${region}
            """

            // Wait for deployment to complete
            echo "[+] Waiting for rolling deployment to complete..."
            sh """
              aws ecs wait services-stable \\
                --cluster ${params.cluster} \\
                --services ${params.servicename} \\
                --region ${region}
            """
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('HEALTH_CHECK') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          // Verify service health
          def serviceStatus = sh(
            script: "aws ecs describe-services --cluster ${params.cluster} --services ${params.servicename} --region ${region} --query 'services[0].deployments[?status==`PRIMARY`].runningCount' --output text",
            returnStdout: true
          ).trim()

          def desiredCount = sh(
            script: "aws ecs describe-services --cluster ${params.cluster} --services ${params.servicename} --region ${region} --query 'services[0].desiredCount' --output text",
            returnStdout: true
          ).trim()

          if (serviceStatus != desiredCount) {
            error "[-] Health check failed. Running count (${serviceStatus}) does not match desired count (${desiredCount})"
          }

          echo "[+] Health check passed. Service is running ${serviceStatus}/${desiredCount} tasks"

          // Optional: Custom health check endpoint
          if (serviceVars.healthCheckUrl) {
            retry(3) {
              sh "curl -f ${serviceVars.healthCheckUrl} || exit 1"
            }
            echo "[+] Application health check passed"
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('UPDATE_CONFIG_AND_CONTEXT') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          dir (variables.CONFIG_REPO.folder) {
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "ECS deployment version updated ${service} - ${releaseEnv} - ${version}", configFilePath)
            }
          }

          // Update Parameter Store with deployment info
          def parameterStore = new ParameterStore(this)
          def deploymentInfo = [
            version: version,
            buildTag: buildTag,
            deploymentTag: params.deploymenttag,
            taskDefinition: newTaskDefArn,
            deployedAt: new Date().format('yyyy-MM-dd HH:mm:ss'),
            deployedBy: env.BUILD_USER ?: 'jenkins'
          ]

          parameterStore.putParameter(
            "/${releaseEnv}/${service}/ecs/deployment-info",
            writeJSON(returnText: true, json: deploymentInfo)
          )

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration

          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('CLEANUP') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          // Clean up old task definitions (keep last 5)
          def taskDefFamily = sh(
            script: "aws ecs describe-task-definition --task-definition ${newTaskDefArn} --region ${region} --query 'taskDefinition.family' --output text",
            returnStdout: true
          ).trim()

          def oldTaskDefs = sh(
            script: "aws ecs list-task-definitions --family-prefix ${taskDefFamily} --status INACTIVE --region ${region} --query 'taskDefinitionArns[:-5]' --output text",
            returnStdout: true
          ).trim()

          if (oldTaskDefs && oldTaskDefs != 'None') {
            oldTaskDefs.split('\t').each { taskDefArn ->
              if (taskDefArn.trim()) {
                sh "aws ecs deregister-task-definition --task-definition ${taskDefArn.trim()} --region ${region}"
                echo "[+] Deregistered old task definition: ${taskDefArn.trim()}"
              }
            }
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          sendProgressMessage(progressNotificationMap)
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }

    failure {
      script {
        // Rollback logic for failed deployments
        if (binding.hasVariable('newTaskDefArn') && newTaskDefArn) {
          echo "[-] Deployment failed. Consider rolling back to previous task definition."

          // Get previous task definition
          def taskDefFamily = sh(
            script: "aws ecs describe-task-definition --task-definition ${newTaskDefArn} --region ${region} --query 'taskDefinition.family' --output text",
            returnStdout: true
          ).trim()

          def previousTaskDefs = sh(
            script: "aws ecs list-task-definitions --family-prefix ${taskDefFamily} --status ACTIVE --region ${region} --query 'taskDefinitionArns[-2]' --output text",
            returnStdout: true
          ).trim()

          if (previousTaskDefs && previousTaskDefs != 'None') {
            echo "[-] Previous task definition available for rollback: ${previousTaskDefs}"
            notificationParams['rollback'] = "Previous task definition: ${previousTaskDefs}"

            // Optional: Automatic rollback
            if (params.autorollback) {
              echo "[!] Performing automatic rollback..."
              sh """
                aws ecs update-service \\
                  --cluster ${params.cluster} \\
                  --service ${params.servicename} \\
                  --task-definition ${previousTaskDefs} \\
                  --region ${region}
              """

              sh """
                aws ecs wait services-stable \\
                  --cluster ${params.cluster} \\
                  --services ${params.servicename} \\
                  --region ${region}
              """

              echo "[+] Rollback completed successfully"
              notificationParams['rollback'] = "Automatic rollback completed to: ${previousTaskDefs}"
            }
          }
        }
      }
    }

    success {
      script {
        echo "[+] ECS deployment completed successfully"
        notificationParams['success'] = "Deployment completed successfully to ${params.cluster}/${params.servicename}"
        notificationParams['taskDefinition'] = newTaskDefArn
        notificationParams['imageTag'] = buildTag
      }
    }
  }
}
