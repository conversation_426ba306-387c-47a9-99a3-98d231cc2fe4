# ECS Deployment Pipeline

This Jenkins pipeline provides automated deployment capabilities for Amazon ECS (Elastic Container Service) following the established Sparrow deployment patterns and integrating with the shared library (`sparrow-libraries@v0.2.2`).

## Overview

The ECS deployment pipeline handles:
- Docker image building and pushing to ECR
- ECS task definition updates
- Service deployments with rolling or blue-green strategies
- Health checks and validation
- Configuration management
- Automatic cleanup and rollback capabilities

## Pipeline Structure

### Stages

1. **INITIALIZE** - Setup variables, notifications, and progress tracking
2. **DEPLOYMENT_PREP** - Clone repository, prepare configurations, and set version
3. **DOCKER_BUILD** - Build and push Docker images to ECR (optional)
4. **UPDATE_TASK_DEFINITION** - Create new ECS task definition with updated image
5. **DEPLOY_TO_ECS** - Deploy to ECS using selected strategy
6. **HEALTH_CHECK** - Verify deployment health and service status
7. **UPDATE_CONFIG_AND_CONTEXT** - Update configuration and parameter store
8. **CLEANUP** - Clean up old task definitions and resources

## Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `releasebranch` | String | Release branch to deploy from | - |
| `deploymenttag` | String | Deployment tag to be deployed | - |
| `cluster` | Choice | ECS Cluster to deploy to | From `variables.ECS_CLUSTERS` |
| `servicename` | String | ECS Service name to deploy | - |
| `deploymenttype` | Choice | Deployment strategy (rolling/blue-green) | rolling |
| `skipbuild` | Boolean | Skip Docker build and use existing image | false |
| `autorollback` | Boolean | Automatically rollback on deployment failure | false |
| `description` | String | Deployment description | - |

## Prerequisites

### Required Variables in Shared Library

The pipeline expects the following variables to be defined in your shared library:

```groovy
variables = [
  ECS_CLUSTERS: ['cluster1', 'cluster2', 'cluster3'],
  ECR_REGISTRY: [
    'us-east-1': '123456789012.dkr.ecr.us-east-1.amazonaws.com',
    'us-west-2': '123456789012.dkr.ecr.us-west-2.amazonaws.com'
  ],
  CLUSTER_REGION_MAP: [
    'cluster1': 'us-east-1',
    'cluster2': 'us-west-2'
  ],
  DEFAULT_REGION: 'us-east-1',
  // ... other existing variables
]
```

### Service Configuration

Each service should be configured in the variables with:

```groovy
[serviceName]: [
  folderName: 'service-repo-folder',
  shortName: 'svc',
  repoUrl: '*****************:company/service.git',
  healthCheckUrl: 'https://service.example.com/health' // optional
]
```

### AWS Permissions

The Jenkins agent requires the following AWS permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "ecs:DescribeServices",
        "ecs:DescribeTaskDefinition",
        "ecs:RegisterTaskDefinition",
        "ecs:UpdateService",
        "ecs:ListTaskDefinitions",
        "ecs:DeregisterTaskDefinition"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:PutParameter"
      ],
      "Resource": "arn:aws:ssm:*:*:parameter/*"
    }
  ]
}
```

## Deployment Strategies

### Rolling Deployment
- Updates tasks gradually
- Maintains service availability
- Uses `maximumPercent=200, minimumHealthyPercent=50`
- Suitable for most applications

### Blue-Green Deployment
- Replaces all tasks at once
- Zero-downtime deployment
- Requires sufficient cluster capacity
- Better for critical applications

## Features

### Health Checks
- Verifies ECS service task counts
- Optional custom health check endpoints
- Automatic retry logic

### Rollback Support
- Manual rollback information provided on failure
- Optional automatic rollback on failure
- Preserves previous task definition for quick recovery

### Configuration Management
- Integrates with existing config repository
- Updates Parameter Store with deployment metadata
- Maintains deployment history

### Cleanup
- Automatically removes old task definitions
- Keeps last 5 revisions for rollback
- Cleans up workspace after deployment

## Usage Examples

### Basic Deployment
```bash
# Deploy latest code with rolling strategy
releasebranch: "release/v1.2.3"
deploymenttag: "v1.2.3"
cluster: "production-cluster"
servicename: "my-service"
deploymenttype: "rolling"
```

### Blue-Green Deployment
```bash
# Deploy with blue-green strategy and auto-rollback
releasebranch: "release/v1.2.3"
deploymenttag: "v1.2.3"
cluster: "production-cluster"
servicename: "my-service"
deploymenttype: "blue-green"
autorollback: true
```

### Skip Build Deployment
```bash
# Deploy existing image without rebuilding
releasebranch: "release/v1.2.3"
deploymenttag: "v1.2.3"
cluster: "production-cluster"
servicename: "my-service"
skipbuild: true
```

## Integration with Existing Infrastructure

This pipeline integrates seamlessly with existing Sparrow infrastructure:

- **Shared Library**: Uses `sparrow-libraries@v0.2.2`
- **Notifications**: Integrates with existing Slack notifications
- **Credentials**: Uses existing credential management
- **Git Operations**: Leverages existing `gitUtils`
- **Docker Operations**: Uses existing `dockerUtils`
- **Parameter Store**: Integrates with existing parameter management

## Monitoring and Troubleshooting

### Logs
- Comprehensive logging at each stage
- Progress notifications with ETC
- Detailed error messages with context

### Notifications
- Slack notifications for start, progress, success, and failure
- Includes deployment metadata and rollback information
- Links to build logs for troubleshooting

### Common Issues
1. **ECR Authentication Failures**: Ensure AWS credentials are properly configured
2. **Task Definition Registration Errors**: Check task definition JSON format
3. **Service Update Failures**: Verify cluster capacity and service configuration
4. **Health Check Failures**: Validate health check endpoints and timeouts

## Future Enhancements

- Support for multiple container definitions
- Integration with AWS Application Load Balancer
- Canary deployment strategy
- Integration with AWS CodeDeploy
- Enhanced monitoring with CloudWatch metrics
