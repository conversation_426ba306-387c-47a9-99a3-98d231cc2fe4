// Example configuration for ECS Deployment Pipeline
// Add these configurations to your shared library variables

// ECS Configuration
ECS_CLUSTERS = [
  'development-cluster',
  'staging-cluster', 
  'production-cluster'
]

ECR_REGISTRY = [
  'us-east-1': '123456789012.dkr.ecr.us-east-1.amazonaws.com',
  'us-west-2': '123456789012.dkr.ecr.us-west-2.amazonaws.com',
  'eu-west-1': '123456789012.dkr.ecr.eu-west-1.amazonaws.com'
]

CLUSTER_REGION_MAP = [
  'development-cluster': 'us-east-1',
  'staging-cluster': 'us-west-2',
  'production-cluster': 'us-east-1'
]

// Service Configurations
// Add your services here following this pattern
'my-service': [
  folderName: 'my-service',
  shortName: 'mysvc',
  repoUrl: '*****************:surveysparrow/my-service.git',
  healthCheckUrl: 'https://my-service.example.com/health'
]

'api-gateway': [
  folderName: 'api-gateway',
  shortName: 'apigw',
  repoUrl: '*****************:surveysparrow/api-gateway.git',
  healthCheckUrl: 'https://api.example.com/health'
]

'user-service': [
  folderName: 'user-service',
  shortName: 'usersvc',
  repoUrl: '*****************:surveysparrow/user-service.git',
  healthCheckUrl: 'https://users.example.com/health'
]

// Example Task Definition Template
// This would typically be stored in your config repository
TASK_DEFINITION_TEMPLATE = [
  family: 'my-service',
  networkMode: 'awsvpc',
  requiresCompatibilities: ['FARGATE'],
  cpu: '256',
  memory: '512',
  executionRoleArn: 'arn:aws:iam::123456789012:role/ecsTaskExecutionRole',
  taskRoleArn: 'arn:aws:iam::123456789012:role/ecsTaskRole',
  containerDefinitions: [
    [
      name: 'my-service',
      image: '123456789012.dkr.ecr.us-east-1.amazonaws.com/my-service:latest',
      portMappings: [
        [
          containerPort: 8080,
          protocol: 'tcp'
        ]
      ],
      essential: true,
      logConfiguration: [
        logDriver: 'awslogs',
        options: [
          'awslogs-group': '/ecs/my-service',
          'awslogs-region': 'us-east-1',
          'awslogs-stream-prefix': 'ecs'
        ]
      ],
      environment: [
        [name: 'NODE_ENV', value: 'production'],
        [name: 'PORT', value: '8080']
      ],
      secrets: [
        [name: 'DATABASE_URL', valueFrom: 'arn:aws:ssm:us-east-1:123456789012:parameter/my-service/database-url']
      ]
    ]
  ]
]

// ECS Service Configuration Template
SERVICE_CONFIGURATION_TEMPLATE = [
  serviceName: 'my-service',
  cluster: 'production-cluster',
  taskDefinition: 'my-service:1',
  desiredCount: 2,
  launchType: 'FARGATE',
  networkConfiguration: [
    awsvpcConfiguration: [
      subnets: [
        'subnet-12345678',
        'subnet-87654321'
      ],
      securityGroups: [
        'sg-12345678'
      ],
      assignPublicIp: 'ENABLED'
    ]
  ],
  loadBalancers: [
    [
      targetGroupArn: 'arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/my-service/1234567890123456',
      containerName: 'my-service',
      containerPort: 8080
    ]
  ],
  deploymentConfiguration: [
    maximumPercent: 200,
    minimumHealthyPercent: 50,
    deploymentCircuitBreaker: [
      enable: true,
      rollback: true
    ]
  ]
]

// Environment-specific overrides
ENVIRONMENT_CONFIGS = [
  development: [
    cpu: '256',
    memory: '512',
    desiredCount: 1
  ],
  staging: [
    cpu: '512', 
    memory: '1024',
    desiredCount: 2
  ],
  production: [
    cpu: '1024',
    memory: '2048', 
    desiredCount: 3
  ]
]

// Deployment notification channels
ECS_NOTIFICATION_CHANNELS = [
  development: '#dev-deployments',
  staging: '#staging-deployments', 
  production: '#production-deployments'
]

// Health check configuration
HEALTH_CHECK_CONFIG = [
  timeout: 30,
  retries: 3,
  interval: 10,
  endpoints: [
    '/health',
    '/health/ready',
    '/health/live'
  ]
]

// Rollback configuration
ROLLBACK_CONFIG = [
  autoRollbackEnabled: false,
  rollbackTimeoutMinutes: 10,
  healthCheckGracePeriod: 60
]
