@Library('sparrow-libraries@v0.3.2') _
variables = loadConstants()

import org.global.ParameterStore
import org.global.S3
import org.global.MathUtils
import org.global.HttpUtils
import org.surveysparrow.SetupUtils

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${env.JOB_NAME} deployment",
      'DC': env.JOB_BASE_NAME,
      'Release branch': params.releasebranch,
      'Deployment tag': params.deploymenttag,
      'Started By': env.BUILD_USER,
      'channel': params.releasebranch.replaceAll('/', '-')
    ]
  }
}

def runMigrations(migrationType) {
  def jobYaml = env.ENVIRONMENT == 'staging'
                        ? "${variables.KUBERNETES_REPO.folder}/kubectl/preproduction/${region}/blue-green-templates/migrations.yaml"
                        : "${variables.KUBERNETES_REPO.folder}/app-v1/kubectl/${region}/blue-green-templates/migrations.yaml"

  dir(variables.CONFIG_REPO.folder) {
    gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
  }

  sh "jq '.version |= \"${version}\"' ${configFilePath} > temp.json"
  k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
  createAndApplyConfigMap (
    cmName: "${serviceVars.namespace}-config-migration",
    namespace: serviceVars.namespace,
    data: [
      "${env.ENVIRONMENT}.json": readFile('temp.json')
    ]
  )

  def jobSelector     = [ "migration_code": "${migrationType}-${serviceVars.shortName}-migration-${env.BUILD_NUMBER}" ]
  def environmentVars = [ BUILD_NUMBER: env.BUILD_NUMBER, MIGRATION_TYPE: migrationType, IMAGE_TAG: params.deploymenttag ]

  dir (variables.KUBERNETES_REPO.folder) {
    gitUtils.cloneRepo(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
  }

  runScriptAsJob (
    filePath: "${env.WORKSPACE}/${jobYaml}",
    selector: jobSelector,
    namespace: serviceVars.namespace,
    log: true,
    logFile: "${migrationType}.txt",
    environment: environmentVars
  )

  def s3Path = "migrationlogs/${region}/${params.deploymenttag}/${migrationType}.txt"
  new S3(this).uploadFile("./${migrationType}.txt", variables.ARTIFACTS_BUCKET, s3Path)
  def s3Uri = "s3://${variables.ARTIFACTS_BUCKET}/${s3Path}"

  def resultsMap = [
    logUrl: sh (returnStdout: true, script: "aws s3 presign ${s3Uri} --region ${variables.DEFAULT_REGION} --expires-in 7200").trim(),
    status: sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'RUNNING_MIGRATION_COMMAND_FAILED_WITH_ERRORS' && exit 1 || exit 0"),
    postMigrationExists: sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'POST_MIGRATION_REQUIRED' && exit 1 || exit 0")
  ]

  return resultsMap
}

def runZipyCli() {
  retry(2) {
    def zipyAuthKey = sh(script: "cat config/${env.ENVIRONMENT}.json | jq '.zipy.authKey'", returnStdout: true).trim()
    def zipyProjKey = sh(script: "cat config/${env.ENVIRONMENT}.json | jq '.zipy.projectKey'", returnStdout: true).trim()

    sh "./node_modules/.bin/zipy-cli --authKey ${zipyAuthKey} --dirname client/dist --apiKey ${zipyProjKey} --releaseVer ${version}"
  }
}

def getJobParams() {
  return [
    string(name: 'releasebranch', description: 'Release branch that was merged.'),
    string(name: 'deploymenttag', description: 'Deployment Tag to be deployed.'),
    booleanParam(name: 'compileassets', defaultValue: true, description: 'If compile assets ?')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

STAGE_COUNT   = 8
CURRENT_STAGE = 0

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label "DockerBuilderFleet"
  }

  tools {
    nodejs "Node-v14.18.2"
  }

  environment {
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          def actualJobName = env.JOB_NAME
          env.JOB_NAME      = "App-v1"

          region                      = variables.DATA_CENTER_REGION_MAP[env.JOB_BASE_NAME]
          releaseEnv                  = variables.REGION_APP_MAP[region] // This is for staging - need to find workaround.
          serviceVars                 = variables[env.JOB_NAME]
          lastSuccessfulBuildDuration = getJobLastSuccessfulDuration(actualJobName)

          progressNotificationMap = [percentage: CURRENT_STAGE, etc: (lastSuccessfulBuildDuration - currentBuild.duration), slackBotToken: env.SLACK_TOKEN, channelId: variables.DEPLOYMENTS_CHANNEL_ID, messageType: 'DEPLOYMENT_STARTED', application: "${env.JOB_NAME}", dc: env.JOB_BASE_NAME]
          messageTimestamp       = sendProgressMessage(progressNotificationMap)
          progressNotificationMap.messageTimestamp = messageTimestamp

          dir(serviceVars.folderName) {
            gitUtils.cloneRepo(params.deploymenttag, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            def commitHash  = gitUtils.getCommitHash()
            versionFromRepo = sh returnStdout: true, script: "echo ${commitHash}${params.releasebranch} | md5sum - | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"
            packageJsonHash = ifInstallNodeModules('package.json', variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}")
          }

          deploymentParameters = [:]
          def parameterStore   = new ParameterStore(this)
          for (def parameter in variables.DEPLOYMENT_PARAMETERS) {
            def parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/${parameter}/green" : "/${region}/${parameter}/green"
            deploymentParameters[parameter] = parameterStore.getParameter(parameterPath)
          }

          version = params.compileassets ? versionFromRepo : deploymentParameters.version

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "${deploymentParameters.deployment} | ${params.releasebranch}"
          postMigrationExists      = true
          setupUtils               = new SetupUtils(this, serviceVars, variables, env.JOB_BASE_NAME)
          configFilePath            = "${variables.CONFIG_REPO.folder}/${env.JOB_BASE_NAME}/${serviceVars.shortName}/${env.ENVIRONMENT}.json"

          setNotificationParams()
          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc        = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('BUILD') {
      parallel {
        stage('WEBPACK_ADMIN_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return params.compileassets }
          }

          steps {
            script {
              setupUtils.prepareRepo(params.deploymenttag, version, packageJsonHash)

              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npm run prod-build:webpack-admin-app
                NODE_ENV=${env.ENVIRONMENT} npm run publish
                """
                runZipyCli()
              }
            }
          }
        }

        stage('WEBPACK_EUI_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return params.compileassets }
          }

          steps {
            script {
              setupUtils.prepareRepo(params.deploymenttag, version, packageJsonHash)
              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p prod-build:webpack-classic-form prod-build:rollup-reputation-embed prod-build:sass
                NODE_ENV=${env.ENVIRONMENT} npm run publish
                """
                runZipyCli()
              }
            }
          }
        }

        stage('WEBPACK_GENERAL_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return params.compileassets }
          }

          steps {
            script {
              setupUtils.prepareRepo(params.deploymenttag, version, packageJsonHash)
              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p prod-build:webpack prod-build:webpack-widget
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p dev-build:vendor-js prod-build:webpack-track_cloudfront_id_sw -s copy:*
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all publish*
                """
                runZipyCli()
              }
            }
          }
        }

        stage('DOCKER_BUILD') {
          agent { label 'DockerBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          steps {
            script {
              setupUtils.prepareRepo(params.deploymenttag, version, packageJsonHash)
              def dockerRegistry = variables.APP_DOCKER_REGISTRY[region]
              def repository     = env.ENVIRONMENT == 'staging' ? releaseEnv : region // To be removed
              def dockerRepo     = dockerRegistry + "/" + repository
              def tags           = [ 'latest', "${params.deploymenttag}" ]

              dir(serviceVars.folderName) {
                sh "rm -rf node_modules/"
                retry(2) {
                  dockerUtils.build("Dockerfiles/${env.ENVIRONMENT}", dockerRepo, tags)
                }
                dockerUtils.authenticate(dockerRegistry, region)
                dockerUtils.push(dockerRepo, tags)
              }
            }
          }
        }
      }
    }

    stage('VERIFY_WEBPACK_BUILD') {
      steps {
        script {
          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          def assetBaseUrl = "${variables.ASSET_REGION_MAP[region]}/${env.ENVIRONMENT}/dist-${version}"
          for (def  asset in variables.APP_V1_CRUCIAL_ASSETS) {
            def assetUrl = "${assetBaseUrl}/${asset}"
            def response = HttpUtils.head(assetUrl)
            if (response.statusCode != 200) {
              error "Asset URL - ${assetUrl} returned status code - ${response.statusCode}"
            }
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('RUN_PRE_MIGRATIONS') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval for pre migrations - ${env.JOB_NAME}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>\n\nPlease note that this is temporary, until migrations check is implemented.\nPlease click on proceed even if migrations are not present in the release."
          notify('APPROVAL', notificationParams, variables)
          input (message: "Proceed to run pre migrations?")

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} deployment"

          def migrationResult = runMigrations("PRE")
          if (migrationResult['status'] != 0) {
            error "PRE Migrations failed!"
          }

          postMigrationExists = migrationResult['postMigrationExists']

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('UPDATE_CONFIG') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval for deployment - ${env.JOB_NAME}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>"
          notify('APPROVAL', notificationParams, variables)
          input (message: "Are you sure to proceed with the deployment?")

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} deployment"

          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          sh "jq '.version |= \"${version}\"' ${configFilePath} > temp.json"
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          createAndApplyConfigMap (
            cmName: "${serviceVars.namespace}-config-${deploymentParameters.configmap}",
            namespace: serviceVars.namespace,
            data: [
              "${env.ENVIRONMENT}.json": readFile('temp.json')
            ]
          )

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('DEPLOY') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          def appName  = variables.REGION_APP_MAP[region]
          def imageSuffix = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}:${params.deploymenttag}" : "/${region}:${params.deploymenttag}"
          def imageUrl = variables.APP_DOCKER_REGISTRY[region] + imageSuffix

          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region) // To be safe
          def workerRollouts = [:]
          for (def deployment in serviceVars.dcLayerMap[region].workerLayers) {
            def currentDeployment = deployment
            workerRollouts[currentDeployment] = {
              def containerName  = "${appName}-${currentDeployment}"
              def deploymentName = "${containerName}-${deploymentParameters.deployment}"
              k8s.editDeploymentImage(deploymentName, containerName, imageUrl, serviceVars.namespace)
              k8s.rolloutStatus(deploymentName, serviceVars.namespace)
            }
          }

          def appRollouts = [:]
          for (def deployment in serviceVars.dcLayerMap[region].appLayers) {
            def currentDeployment = deployment
            appRollouts[currentDeployment] = {
              def containerName  = "${appName}-${currentDeployment}"
              def deploymentName = "${containerName}-${deploymentParameters.deployment}"
              k8s.editDeploymentImage(deploymentName, containerName, imageUrl, serviceVars.namespace)
              k8s.rolloutStatus(deploymentName, serviceVars.namespace)
            }
          }

          parallel workerRollouts
          parallel appRollouts

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('UPDATE_PARAMETER_STORE') {
      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          deploymentParameters['version'] = version
          deploymentParameters['image']   = params.deploymenttag

          def updatedParameters = ['image', 'version']
          def parameterStore    = new ParameterStore(this)
          for (def parameter in updatedParameters) {
            def parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/${parameter}/green" : "/${region}/${parameter}/green"
            parameterStore.putParameter(parameterPath, deploymentParameters[parameter])
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }

    stage('RUN_POST_MIGRATIONS') {
      when {
        expression { return postMigrationExists }
      }

      steps {
        script {
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)

          notificationParams.pipeline = "Awaiting approval for post migrations - ${env.JOB_NAME}"
          notificationParams.logs     = "<${env.BUILD_URL}input|Click here to proceed>"
          notify('APPROVAL', notificationParams, variables)
          input (message: "Proceed to run the post migrations?")

          notificationParams.remove('logs')
          notificationParams.pipeline = "${env.JOB_NAME} deployment"

          def migrationResult = runMigrations("POST")
          if (migrationResult['status'] != 0) {
            error "POST Migrations failed"
          }

          progressNotificationMap.percentage = MathUtils.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          progressNotificationMap.etc = lastSuccessfulBuildDuration - currentBuild.duration
          sendProgressMessage(progressNotificationMap)
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        if (env.ENVIRONMENT == 'production') {
          build (
            job: serviceVars.automation.jobName,
            parameters: [
              string ( name: 'TestBranch', value: "develop"),
              string ( name: 'TestingEnvironment', value: "${serviceVars.automation.dcOptionsMap[env.JOB_BASE_NAME]}"),
              string ( name: 'AutomationType', value: 'OTHERS'),
              string ( name: 'TestCases', value: 'production_sanity:30')
            ],
            wait: false
          )
        }

        progressNotificationMap.percentage = 100
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_SUCCESSFUL'
        sendProgressMessage(progressNotificationMap)
        cleanWs()
      }
    }

    failure {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_FAILED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('FAILED', notificationParams, variables)
        cleanWs()
      }
    }

    aborted {
      script {
        progressNotificationMap.percentage = MathUtils.calculatePercentage(CURRENT_STAGE, STAGE_COUNT)
        progressNotificationMap.etc = currentBuild.duration
        progressNotificationMap.messageType = 'DEPLOYMENT_ABORTED'
        sendProgressMessage(progressNotificationMap)

        notificationParams.logs = "<${env.BUILD_URL}console|Click here to view logs>"
        notify('ABORTED', notificationParams, variables)
        cleanWs()
      }
    }
  }
}
