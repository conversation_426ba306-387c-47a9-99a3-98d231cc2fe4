@Library('sparrow-libraries@v0.4.0') _

variables = loadConstants()

import org.global.ParameterStore
import org.global.HttpUtils
import org.global.S3
import org.global.ReleaseUtils
import org.global.CodeMagic

appName = env.JOB_NAME.split('/')[1]
appConfig = variables.MOBILE.IOS_CODE_MAGIC[appName]

notificationConfig = [
  botUser: true
]

artificatExportNotificationConfig = [
  botUser: true,
  emoji: ":scroll:"
]

artificatExportNotificationParams = [
  "pipeline": "Artifact export"
]

void setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "IOS deployment",
      'Branch': branch,
      'Build Scheme': buildScheme,
      'Started By': env.BUILD_USER,
      'channel': appConfig.slackChannel
    ]
  }
}

void setPipelineInfo() {
  currentBuild.displayName = "${branch} | ${env.BUILD_NUMBER}"
  currentBuild.description = "Code Magic Status: ${buildStatus.toUpperCase()} | Build Scheme: ${buildScheme} | Bump Type: ${params.bumptype} | Release Version: ${version.newMarketingVersion}"
}

List<Object> genParameters() {
  List<Object> _parameters = [
    choice(
      name: 'serviceendpoint',
      choices: appConfig.backendScheme,
      description: 'Select Build Scheme'
    ),
    choice(
      name: 'workflow',
      description: 'Select Workflow:',
      choices: appConfig.workflowIds
    )
  ]

  if (env.ENVIRONMENT == 'staging') {
    _parameters.add(string(
      name: 'branchname',
      description: 'Branch to deploy',
      defaultValue: 'master',
    ))
  }

  if (!env.JOB_BASE_NAME.contains('QA')) {
    _parameters.add(choice(
      name: 'bumptype',
      description: 'Select the Release type:',
      choices: variables.VERSION_BUMP_TYPES
    ))
  }

  return _parameters
}

properties([
  disableConcurrentBuilds(),
  buildDiscarder(
    logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')
  ),
  parameters(genParameters())
])

// Actual pipeline starts here
pipeline {

  agent { label 'AndroidBuilderFleet' }

  environment {
    CODE_MAGIC_API_TOKEN = credentials('CodeMagicAPIToken')
  }

  stages {
    stage("Initialize Pipeline") {
      steps {
        script {
          branch = params.branchname ?: variables.DEFAULT_BRANCH
          buildScheme = params.serviceendpoint
          workflowId = params.workflow
          bumpType = params.bumptype ?: variables.DEFAULT_BUMP_TYPE
          version = [:]

          codeMagic = new CodeMagic(this, appConfig.codeMagicAppId, workflowId, CODE_MAGIC_API_TOKEN)
          parameterStore = new ParameterStore(this)
          s3 = new S3(this)
          buildStatus = 'not_started'
          setPipelineInfo()
          setNotificationParams()
          startedThreadId = notify("STARTED", notificationParams, variables, notificationConfig).threadId
          artificatExportNotificationParams['channel'] = startedThreadId
        }
      }
    }

    stage("Get & Set Version Info") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      steps {
        script {
          withAWS(roleAccount: variables.PRODUCTION_AWS_ACCOUNT_ID, region: variables.DEFAULT_REGION, role: variables.MOBILE.IAM_ROLE_NAME) {
            version['marketingVersion'] = parameterStore.getParameter(appConfig.iosMarketingVersionSSMPath)
            version['bundleVersion'] = parameterStore.getParameter(appConfig.iosBundleVersionSSMPath)
          }
          ReleaseUtils releaseUtils = new ReleaseUtils(this)
          version['newMarketingVersion'] = releaseUtils.bumpVersion(version['marketingVersion'], bumpType)
          version['newBundleVersion'] = (version['bundleVersion'].toInteger() + 1).toString()
        }
      }
    }

    stage("Trigger CodeMagic Build") {
      options {
        timeout(time: 1, unit: 'MINUTES')
      }
      steps {
        script {
          Map<String, Object> buildPayload = [
            'branch': branch,
            'workflowId': workflowId,
            'appId': appConfig.codeMagicAppId,
            'environment': [
              'variables': [
                'SCHEME': buildScheme,
                'NEW_VERSION': version.newMarketingVersion,
                'NEW_BUILD_NUMBER': version.newBundleVersion
              ]
            ],
            'labels': [buildScheme]
          ]
          Map<String, Object> buildInfo = codeMagic.triggerCodeMagicDeployment(buildPayload)
          buildId = buildInfo.buildId
          int responseCode = buildInfo.responseCode
          if (responseCode != 200) {
            error "CodeMagic deployment failed with response code: ${responseCode}"
          }
          setPipelineInfo()
        }
      }
      post {
        aborted {
          script {
            codeMagic.cancelCodeMagicDeployment()
          }
        }
      }
    }

    stage("Watch CodeMagic Build") {
      options {
        timeout(time: 40, unit: 'MINUTES')
      }
      steps {
        script {
          while (true) {
            Map<String, Object> response = codeMagic.getDeploymentStatus(buildId)
            Map<String, Object> buildData = response.body
            int responseCode = response.responseCode
            if (responseCode != 200) {
              error "Failed to check CodeMagic deployment status: ${responseCode}"
            }
            buildStatus = buildData.build.status
            codeMagic.printCompletedBuildStageLogs(buildData)
            if (buildStatus in ['finished', 'failed', 'canceled', 'timeout']) {
              break
            }
            sh "sleep 30"
          }
          setPipelineInfo()
          if (buildStatus != 'finished') {
            error "CodeMagic deployment failed with status: ${buildStatus}"
          }
        }
      }
      post {
        always {
          script {
            bucketPrefix = "${appName}/${buildId}"
            String fileName = "CodeMagicLogs.txt"
            String bucketPath = "${bucketPrefix}/${fileName}"
            if (fileExists(fileName)) {
              s3.uploadFile(fileName, variables.ARTIFACTS_BUCKET, bucketPath)
              String preSignedUrl = s3.getPreSignedUrl(bucketPath, variables.ARTIFACTS_BUCKET, variables.S3_PRESIGNED_URL_EXPIRATION)
              
              artificatExportNotificationParams['Build Logs'] = "<${preSignedUrl}|Download Build Logs>"
              notify("SUCCESS", artificatExportNotificationParams, variables, artificatExportNotificationConfig)
              artificatExportNotificationParams.remove('Build Logs')
            }
          }
        }
        aborted {
          script {
            codeMagic.cancelCodeMagicDeployment()
          }
        }
      }
    }

    stage("Update Version Info") {
      options {
        timeout(time: 3, unit: 'MINUTES')
      }
      steps {
        script {
          notificationParams['New Version'] = version.newMarketingVersion
          notificationParams['New Bundle Version'] = version.newBundleVersion

          withAWS(roleAccount: variables.PRODUCTION_AWS_ACCOUNT_ID, region: variables.DEFAULT_REGION, role: variables.MOBILE.IAM_ROLE_NAME) {
            parameterStore.putParameter(appConfig.iosMarketingVersionSSMPath, version.newMarketingVersion)
            parameterStore.putParameter(appConfig.iosBundleVersionSSMPath, version.newBundleVersion)
          }
        }
      }
    }

    stage("Publish Artifact") {
      steps {
        script {
          String artifactUrl = codeMagic.getArtifactDownloadUrl()
          if (!(artifactUrl instanceof net.sf.json.JSONNull)) {
            String artifactFileName = "${appName}_${version.newBundleVersion}.ipa"
            sh "wget \"${artifactUrl}\" -O ${artifactFileName}"
            String bucketPath = "${bucketPrefix}/${artifactFileName}"

            s3.uploadFile(artifactFileName, variables.ARTIFACTS_BUCKET, bucketPath)
            String preSignedUrl = s3.getPreSignedUrl(bucketPath, variables.ARTIFACTS_BUCKET, variables.S3_PRESIGNED_URL_EXPIRATION)
            
            artificatExportNotificationParams['IPA'] = "<${preSignedUrl}|Download IPA>"
            artificatExportNotificationConfig['emoji'] = ":package:"
            notify("SUCCESS", artificatExportNotificationParams, variables, artificatExportNotificationConfig)
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notify(currentBuild.currentResult, notificationParams, variables, notificationConfig)
        cleanWs()
      }
    }
  }
}
