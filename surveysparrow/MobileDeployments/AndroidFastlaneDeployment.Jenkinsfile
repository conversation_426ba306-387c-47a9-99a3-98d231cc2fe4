@Library('sparrow-libraries@v0.4.0') _
variables = loadConstants()

import org.global.ParameterStore
import org.global.S3
import org.global.SecretManager
import org.global.ReleaseUtils

appName = env.JOB_NAME.split('/')[1]
appConfig = variables.MOBILE.FASTLANE[appName]

notificationConfig = [
  botUser: true
]

artificatExportNotificationConfig = [
  botUser: true,
  emoji: ":scroll:"
]

artificatExportNotificationParams = [
  "pipeline": "Artifact export"
]

void setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "Android deployment",
      'Branch': branch,
      'Lane': lane,
      'Build Environment': buildEnvironment,
      'Bump Type': bumpType,
      'Started By': env.BUILD_USER,
      'Release Note': params.releasenote,
      'channel': appConfig.slackChannel
    ]
  }
}

void setPipelineInfo() {
  currentBuild.displayName = "${branch} | ${env.BUILD_NUMBER}"
  currentBuild.description = "Lane: ${lane} | Build Environment: ${buildEnvironment} | Bump Type: ${params.bumptype}| Release Version: ${newMarketingVersion}"
}

List<Object> genParameters() {
  List<Object> _parameters = [
    choice(
      name: 'buildtype',
      description: 'Enter the Build Environment:',
      choices: appConfig.lanes
    ),
    choice(
      name: 'serverendpoint',
      description: 'Name of Server Endpoint',
      choices: appConfig.backendEnv.keySet().toList()
    )
  ]

  if(env.ENVIRONMENT == 'staging'){
    _parameters.add(string(
      name: 'branchname',
      description : 'Enter the branch to be deployed',
      defaultValue : 'master',
    ))
  }

  if(!env.JOB_BASE_NAME.contains('QA')) {
    _parameters.add(choice(
      name: 'bumptype',
      description: 'Select the Release type:',
      choices: variables.VERSION_BUMP_TYPES
    ))
  }

  _parameters.add(text(
    name: 'releasenote',
    description: 'The detailed features about this Release:',
    defaultValue: 'No release note provided'
  ))

  _parameters.add(
    booleanParam(
      name: 'apkexport',
      description: 'Do you want to export APK?',
      defaultValue: false,
    )
  )

  return _parameters
}

properties([
  disableConcurrentBuilds(),
  buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
  parameters(genParameters())
])

// Actual pipeline starts here
pipeline {
  agent { label 'AndroidBuilderFleet' }

  environment {
    SIGNING_PASSWORD = credentials('OfflineAndroidSigningKey')
  }

  stages {
    stage("Initialize Pipeline") {
      steps {
        script {
          branch = params.branchname ?: variables.DEFAULT_BRANCH
          lane = params.buildtype
          buildEnvironment = params.serverendpoint
          bumpType = params.bumptype ?: variables.DEFAULT_BUMP_TYPE
          newMarketingVersion = "not set"
          
          parameterStore = new ParameterStore(this)

          setPipelineInfo()
          setNotificationParams()
          
          startedThreadId = notify("STARTED", notificationParams, variables, notificationConfig).threadId
          artificatExportNotificationParams['channel'] = startedThreadId
        }
      }
    }

    stage("Git clone") {
      options {
        timeout(time: 3, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            gitUtils.cloneRepo(branch, appConfig.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }
        }
      }
    }

    stage("Get Version from Parameter Store") {
      options {
        timeout(time: 2, unit: 'MINUTES')
      }
      steps {
        script {
          withAWS(roleAccount: variables.PRODUCTION_AWS_ACCOUNT_ID, region: variables.DEFAULT_REGION, role: variables.MOBILE.IAM_ROLE_NAME) {
            releaseVersion = parameterStore.getParameter(appConfig.androidMarketingSSMPath)
            bundleVersion  = parameterStore.getParameter(appConfig.androidBundleSSMPath)
            SecretManager secretManager = new SecretManager(this)
            secrets = secretManager.fetchSecrets(appConfig.secretName) // the output file will be {"service_account": "json data of service account", "certificate": "base64 encoded certificate"}
          }
          dir(appConfig.folderName) {
            Map<String, Object> parsedSecrets = readJSON text: secrets
            String googlePlayKey = parsedSecrets.service_account 
            String encodedCertificate = parsedSecrets.certificate

            writeFile file: 'google_play_api_key.json', text: googlePlayKey

            String certificateFile = 'certificate.encoded'
            writeFile file: certificateFile, text: encodedCertificate
            sh "base64 -d ${certificateFile} > deployment_keys && rm ${certificateFile}"
          }
          ReleaseUtils releaseUtils = new ReleaseUtils(this)
          newMarketingVersion = releaseUtils.bumpVersion(releaseVersion, bumpType)
          newBundleVersion = (bundleVersion.toInteger() + 1).toString()
          setPipelineInfo()
        }
      }
    }

    stage("Installing Dependencies") {
      options {
        timeout(time: 15, unit: 'MINUTES')
      }
      steps {
        script {
          dir(appConfig.folderName) {
            parallel(
              "installNode": {
                retry(2) {
                  sh "yarn install"
                }
              },
              "installRuby": {
                sh "bundle install"
              }
            )
          }
        }
      }
    }

    stage("Deployment of Android Bundle") {
      options {
        timeout(time: 40, unit: 'MINUTES')
      }
      steps {
        dir(appConfig.folderName) {
          script {
            sh "STORE_PASSWORD=${SIGNING_PASSWORD} KEY_PASSWORD=${SIGNING_PASSWORD} bundle exec fastlane ${lane} \
              env:${buildEnvironment} \
              version_name:'${newMarketingVersion}' \
              version_code:'${newBundleVersion}' \
              test_note:'${params.releasenote}' \
              release_note:'${params.releasenote}' | tee fastlane.log"
            
            sh "cat fastlane.log | grep 'BUILD SUCCESSFUL in'"
            
            if(fileExists("fastlane/downloadUrl.txt")) {
              notificationParams['Download Url'] = readFile("fastlane/downloadUrl.txt").trim()
            }

            bucketPrefix = "${appName}/${newBundleVersion}"
            String fileName = "fastlane.log"
            String bucketPath = "${bucketPrefix}/${fileName}"
            s3 = new S3(this)
            s3.uploadFile(fileName, variables.ARTIFACTS_BUCKET, bucketPath)
            String preSignedUrl = s3.getPreSignedUrl(bucketPath, variables.ARTIFACTS_BUCKET, variables.S3_PRESIGNED_URL_EXPIRATION)

            artificatExportNotificationParams['Build Logs'] = "<${preSignedUrl}|Download Build Logs>"            
            notify("SUCCESS", artificatExportNotificationParams, variables, artificatExportNotificationConfig)
            artificatExportNotificationParams.remove('Build Logs')
          }
        }
      }
    }
    stage("Update Parameter Store") {
      options {
        timeout(time: 5, unit: 'MINUTES')
      }
      steps {
        script {
          notificationParams['New Version'] = newMarketingVersion
          notificationParams['New Bundle Version'] = newBundleVersion
          
          setPipelineInfo()

          withAWS(roleAccount: variables.PRODUCTION_AWS_ACCOUNT_ID, region: variables.DEFAULT_REGION, role: variables.MOBILE_IAM_ROLE_NAME) {
            parameterStore.putParameter(appConfig.androidMarketingSSMPath, newMarketingVersion)
            parameterStore.putParameter(appConfig.androidBundleSSMPath, newBundleVersion)
          }
        }
      }
    }

    stage("Upload APK to S3 and Notify Slack") {
      options {
        timeout(time: 10, unit: 'MINUTES')
      }
      when {
        expression { params.apkexport }
      }
      steps {
        script {
          dir("${appConfig.folderName}/android") {
            sh "./gradlew assemble${appConfig.backendEnv[buildEnvironment].capitalize()} \
               -PstorePassword=${SIGNING_PASSWORD} \
               -PkeyPassword=${SIGNING_PASSWORD} \
               -PversionCode=${newBundleVersion} \
               -PversionName=${newMarketingVersion}"

            String debugPath = "app/build/outputs/apk/debug/app-debug.apk"
            String releaseName = appConfig.backendEnv[buildEnvironment]
            String releasePath = "app/build/outputs/apk/${releaseName}/app-${releaseName}.apk"
            String apkPath = fileExists(debugPath) ? debugPath : fileExists(releasePath) ? releasePath : ""

            if(apkPath) {
              bucketPrefix = "${appName}/${newBundleVersion}"
              String fileName = apkPath.split('/').last()
              String bucketPath = "${bucketPrefix}/${fileName}"
              
              s3.uploadFile(apkPath, variables.ARTIFACTS_BUCKET, bucketPath)
              String preSignedUrl = s3.getPreSignedUrl(bucketPath, variables.ARTIFACTS_BUCKET, variables.S3_PRESIGNED_URL_EXPIRATION)

              artificatExportNotificationParams['APK'] = "<${preSignedUrl}|Download APK>"
              notify("SUCCESS", artificatExportNotificationParams, variables, artificatExportNotificationConfig)
            }
          }
        }
      }
    }
  }

  post {
    always {
      script { 
        notify(currentBuild.currentResult, notificationParams, variables) 
        cleanWs()
      }
    }
  }
}

