@Library('sparrow-libraries@temp-migration-worker') _
variables = loadConstants()

import org.global.ParameterStore

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} worker queue update",
      'Region': params.region,
      'Layer': params.layer,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def updateEnv(yamlFile, workerConfig, labelValue) {
  def kubeYaml  = readYaml file: yamlFile
  def documents = kubeYaml instanceof List ? kubeYaml : [kubeYaml]

  documents.collect { deployment ->
    def appLabel   = deployment.metadata.labels.app

    if (appLabel == labelValue) {
      deployment.spec.template.spec.containers[0].env.each { env ->
        if (env.name == 'NODE_CONFIG') {
          env.value = writeJSON(returnText: true, json: workerConfig)
        }
      }
    }

    return deployment
  }

  def updatedYaml = documents.collect { writeYaml returnText: true, data: it }.join("\n---\n")
  return updatedYaml
}

def getJobParams() {
  return [
    choice(name: 'region', choices: variables.REGION_NAMES, description: 'Specify which region to update workers'),
    choice(name: 'layer', choices: variables.LAYERS.worker, description: 'Select the worker to update the queues'),
    string(name: 'reason', description: 'Specify the reason to update the worker configuration')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else if (serviceIdentifier[0] == 'MicroServices') {
            service = serviceIdentifier[1]
            serviceVars = variables[service]['backend']
          }

          region = variables.DATA_CENTER_REGION_MAP[params.region]
          setNotificationParams()
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.reason}"

          if (!serviceVars.dcLayerMap[region].workerLayers.contains(params.layer)) {
            error "Layer - ${params.layer} is not present the region - ${params.region}"
          }
        }
      }
    }

    stage('CLONE_REPOS') {
      steps {
        script {
          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          dir(variables.KUBERNETES_REPO.folder) {
            gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }
        }
      }
    }

    stage('UPDATING_YAML') {
      steps {
        script {
          def yamlBasePath = env.ENVIRONMENT == 'staging'
                              ? variables.KUBERNETES_REPO.folder + "/kubectl/preproduction/"
                              : variables.KUBERNETES_REPO.folder + "/app-v1/kubectl/"

          workerYamlFilePath = ""
          if (serviceVars.service == 'app-v1') {
            workerYamlFilePath = yamlBasePath + "${region}/blue-green-templates/worker.yaml"
          } else {
            workerYamlFilePath = yamlBasePath + "${region}/${serviceVars.shortName}/worker.yaml"
          }

          if (serviceVars.service == 'app-v1') {
            def parameterBase    = env.ENVIRONMENT == 'staging' ? "/${variables.REGION_APP_MAP[region]}" : "/${region}"
            def deploymentParams = [:]
            def requiredParams   = [ "deployment", "image", "app" ]
            def parameterStore   = new ParameterStore(this)
            for (def param in requiredParams) {
              def parameterPath = "${parameterBase}/${param}/green"
              deploymentParams[param] = parameterStore.getParameter(parameterPath)
            }

            sh "STACK=${deploymentParams.deployment} TAG=${deploymentParams.app} IMAGE_TAG=${deploymentParams.image} envsubst < ${workerYamlFilePath} > workerToApply.yaml"
          } else {
            sh "cp ${workerYamlFilePath} workerToApply.yaml"
          }

          def appName = variables.REGION_APP_MAP[region]
          labelValue  = serviceVars.service == 'app-v1' ? "${appName}-${params.layer}" : "${appName}-${serviceVars.shortName}-${params.layer}"

          def workerConfigPath = variables.CONFIG_REPO.folder + "/${params.region}/${serviceVars.shortName}/${params.layer}.json"
          workerConfig         = readJSON file: workerConfigPath

          def updatedEnv = updateEnv('workerToApply.yaml', workerConfig, labelValue)
          writeFile file: 'updated-deployment.yaml', text: updatedEnv
        }
      }
    }

    stage('APPLY_AND_COMMIT') {
      steps {
        script {
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          k8s.apply("updated-deployment.yaml")
          k8s.clearAuth()

          if (serviceVars.service == 'app-v1') {
            def sampleEnv = updateEnv(workerYamlFilePath, workerConfig, labelValue)
            writeFile file: 'updated-deployment.yaml', text: sampleEnv
          }

          sh """
          rm -f ${workerYamlFilePath}
          cp updated-deployment.yaml ${workerYamlFilePath}
          """

          dir (variables.KUBERNETES_REPO.folder) {
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Updated ${params.layer} for - ${serviceVars.shortName} - ${params.reason}")
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
