@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "Squad branch creation",
      'Branch': params.branchname,
      'Services': services.join(', '),
      'Started By': env.BUILD_USER,
    ]
  }
}

def getJobParams() {
  def jobParams = []

  def repos = variables.SUBMODULES.keySet().toList() + variables.CONFIG_REPO.folder + variables.PRODUCTION_CONFIG_REPO.folder
  repos = "[" + repos.collect { "\"$it\"" }.join(", ") + "]"

  jobParams.add(
    string (name: 'branchname', description: "Branch to create")
  )

  jobParams.add(
    activeChoice(
      choiceType: 'PT_CHECKBOX',
      description: 'Select the repos in which the branch needs to be created.',
      filterLength: 1, 
      filterable: false, 
      name: 'services', 
      randomName: 'choice-parameter-704386024516185', 
      script: groovyScript(
        fallbackScript: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: ''
        ], 
        script: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: repos.toString()
        ]
      )
    )
  )

  return jobParams
}

def getProperties() {
  return defaultProperties = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
    parameters(getJobParams())
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  environment {
    JOB_NAME = "${env.JOB_NAME.split('/')[2]}"
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          branchName = params.branchname.trim()
          services   = params.services.split(",")

          if (!branchName.startsWith(variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch'])) {
            error "[-] The branch should start with ${variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']}. Aborting deployment."
          }
        }
      }
    }

    stage('CREATE_BRANCH') {
      steps {
        script {
          for (service in services) {
            dir (service) {
              gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, "*****************:surveysparrow/${service}.git", variables.BITBUCKET_CREDENTIAL_ID)
              sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
                if (variables.SUBMODULES[service] != null && variables.SUBMODULES[service].size() > 0) {
                  sh "git submodule update --init --recursive"
                }

                gitUtils.createBranch(branchName)
              }

              for (def submodule in variables.SUBMODULES[service]) {
                dir (submodule) {
                  sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
                    gitUtils.createBranch(branchName)
                  }
                }
              }

              sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
                if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                  echo "[+] Nothing changed no commits to be made"
                } else {
                  gitUtils.commitAndPush(branchName, 'Update submodule reference')
                }
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        setNotificationParams()
        notificationParams['channel'] = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}