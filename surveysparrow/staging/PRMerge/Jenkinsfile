@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} PR Merge",
      'Tickets': params.ticketids,
      'SourceBranch': params.branchname,
      'ReleaseBranch': releaseBranch,
      'Started by': env.BUILD_USER,
      'channel': releaseBranch
    ]
  }
}

def getJobParams() {
  return [
    string(name: 'branchname', description: 'Squad branch name to be merged to release branch.', trim: true),
    string(name: 'ticketids', description: 'List of ticket ids that are merged (separate by commas)', trim: true)
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  environment {
    JIRA_TOKEN = credentials('JiraAuthToken')
    BB_TOKEN   = credentials('BitBucketOAuthToken')
  }

  stages {
    stage('VERIFY_PR_IN_ALL_REPOS') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service     = serviceIdentifier[0]
            serviceVars = variables[service]
          }
          if (serviceIdentifier[0] == "MicroServices") {
            service     = serviceIdentifier[1]
            serviceVars = variables[service][serviceIdentifier[2]]
          }

          loadResourceFile('global/bitbuckethelper.py')
          def prChecks = [:]
          prChecks['mainPr'] = {
            mainPrId = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-prid -s ${params.branchname}", returnStdout: true).trim()
          }
          prChecks['stagConfig'] = {
            stagPrId = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${variables.CONFIG_REPO.folder} get-prid -s ${params.branchname}", returnStdout: true).trim()
          }
          prChecks['prodConfig'] = {
            prodPrId = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${variables.PRODUCTION_CONFIG_REPO.folder} get-prid -s ${params.branchname}", returnStdout: true).trim()
          }

          parallel prChecks

          configUpdated = false

          if (mainPrId) {
            releaseBranch = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-targetbranch -s ${params.branchname}", returnStdout: true).trim()
            def diffStat  = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-diff -p ${mainPrId}", returnStdout: true).trim()
            def diffJson  = readJSON(text: diffStat)
            configUpdated  = diffJson.new.any { it.contains('config/') } ||
                            diffJson.old.any { it.contains('config/') }
          }

          if ((!mainPrId) && (stagPrId || prodPrId)) {
            def repository = stagPrId ? variables.CONFIG_REPO.folder : variables.PRODUCTION_CONFIG_REPO.folder
            releaseBranch = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${repository} get-targetbranch -s ${params.branchname}", returnStdout: true).trim()
          }

          setNotificationParams()
          notify("STARTED", notificationParams, variables)

          if (mainPrId && configUpdated && !stagPrId && !prodPrId) {
            notificationParams['pipeline'] = "*CONFIG PR is not raised*"
            notificationParams['logs']     = "\n\n*Please raise the Config PR from `${params.branchname}` to `${releaseBranch}` and retrigger the pipeline."

            notify("APPROVAL", notificationParams, variables)

            notificationParams['pipeline'] = "${service} PR Merge"
            notificationParams.remove('logs')

            error "CONFIG PR NOT RAISED"
          }
        }
      }
    }

    stage('VERIFY_TICKETS_IN_RELEASE') {
      steps {
        script {
          loadResourceFile('global/jirahelper.py')
          def issueKeys = sh(returnStdout: true, script: "RELEASE_BRANCH=${releaseBranch} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} python3 jirahelper.py -k").trim()
          // the above prints the python array = ['SSE-1234'] - the below replaces [, ', ] characters.
          def issueSet  = issueKeys.replaceAll(/[\[\]']/, '').split(',').collect { it.toUpperCase().trim() } as Set
          def ticketSet = params.ticketids.split(',').collect { it.toUpperCase().trim() } as Set

          def missingTickets = ticketSet - issueSet

          if (missingTickets.size() > 0) {
            notificationParams['error'] = "Tickets - ${missingTickets.join(', ')} is / are not in the release ${releaseBranch}. Please add fix version to those tickets."
            error notificationParams['error']
          }
        }
      }
    }

    stage('MERGE_STAGING_CONFIG') {
      when {
        expression { return stagPrId }
      }

      steps {
        script {
          def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${variables.CONFIG_REPO.folder} merge-prs -p ${stagPrId}", returnStatus: true)
          if (exitStatus != 0) {
            notificationParams['error'] = "Staging config merge failed."
            error notificationParams['error']
          }
        }
      }
    }

    stage('MERGE_PRODUCTION_CONFIG') {
      when {
        expression { return prodPrId }
      }

      steps {
        script {
          def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${variables.PRODUCTION_CONFIG_REPO.folder} merge-prs -p ${prodPrId}", returnStatus: true)
          if (exitStatus != 0) {
            notificationParams['error'] = "Production config merge failed."
            error notificationParams['error']
          }
        }
      }
    }

    stage('MERGE_MAIN_PR') {
      when {
        expression { return mainPrId }
      }

      steps {
        script {
          for (def submodule in variables.SUBMODULES[serviceVars.service]) {
            def submodulePr = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} get-prid -s ${params.branchname}", returnStdout: true).trim()
            if (submodulePr) {
              def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} merge-prs -p ${submodulePr}", returnStatus: true)
              if (exitStatus != 0) {
                notificationParams['error'] = "${submodule} PR merge failed."
                error notificationParams['error']
              }
            }
          }

          def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} merge-prs -p ${mainPrId}", returnStatus: true)
          if (exitStatus != 0) {
            notificationParams['error'] = "${serviceVars.service} PR merge failed."
            error notificationParams['error']
          }

          dir(serviceVars.folderName) {
            gitUtils.cloneRepoWithGit(releaseBranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              sh "git submodule update --init --recursive"

              for (submodule in variables.SUBMODULES[serviceVars.folderName]) {
                dir(submodule) {
                  if (sh (script: "git ls-remote | grep refs/heads/${releaseBranch}\$", returnStatus: true) == 0) {
                    sh """
                    git remote update
                    git fetch --all
                    git fetch origin ${releaseBranch}
                    """

                    try {
                      sh """
                      git checkout --track origin/${releaseBranch}
                      """
                    } catch (Exception e) {
                      echo "Error checking out ${releaseBranch} in ${submodule} - ${e}"
                      sh """
                      git checkout ${releaseBranch}
                      git pull
                      """
                    }
                  }
                }
              }

              gitUtils.setGitConfig()
              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                gitUtils.commitAndPush(releaseBranch, "Update submodule reference - merged ${params.branchname} to ${releaseBranch}")
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
