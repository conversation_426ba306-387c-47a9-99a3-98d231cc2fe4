@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

def setNotificationParams(config = [:]) {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${params.deployment} config update",
      'Environment': config.releaseEnv,
      'Started By': env.BUILD_USER,
      'channel': config.channel
    ]
  }
}

def getJobParams() {
  def jobParams = []
  if (env.JOB_NAME.contains('CommonPool')) {
    jobParams.add(
      choice(name: 'squad', choices: variables.SQUADS, description: 'Choose the squad name')
    )
  }

  jobParams.add(
    choice (name: 'deployment', choices: ["App-v1", "haproxy"] + variables.DEPLOYED_SOCS, description: 'Choose the deployment to update config')
  )

  return jobParams
}

def getJobProperties() {
  def defaultProps = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '2'))
  ]

  defaultProps.add(parameters(getJobParams()))

  return defaultProps
}

properties(getJobProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  environment {
    JOB_NAME    = "${env.JOB_NAME.split('/')[2]}"
    ARGOCD_CRED = credentials("${variables.ARGOCD.credentialId}")
  }

  stages {
    stage('INITIALIZE_PIPELINE') {
      steps {
        script {
          service     = params.deployment

          if (service == "App-v1") {
            serviceVars = variables[service]
          }
          if (variables.DEPLOYED_SOCS.contains(service)) {
            serviceVars = variables[service]['backend']
          }

          def notificationConfig = [
            releaseEnv: variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['env'],
          ]

          if (params.squad) {
            def squadVariables = variables.SQUAD_BRANCH_ENV_MAP.find { squad, details -> squad.contains(params.squad) }?.value
            notificationConfig['channel'] = squadVariables.branch
          } else {
            notificationConfig['channel'] = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']
          }

          setNotificationParams(notificationConfig)
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${service}"
        }
      }
    }

    stage('UPDATE_CONFIG') {
      steps {
        argocd (
          action: variables.ARGOCD.ACTIONS.configUpdate,
          argoCdUsername: ARGOCD_CRED_USR,
          argoCdPassword: ARGOCD_CRED_PSW,
          argoCdAppName: variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['appName'],
          argoCdDomain: variables.ARGOCD.domain
        )
      }
    }

    stage('RESTART_CONTAINERS') {
      steps {
        script {
          k8s.authenticate(variables.CLUSTER_REGION_MAP[variables.DEFAULT_REGION], variables.DEFAULT_REGION)
          def restarts = [:]
          if (service == 'haproxy') {
            restarts['haproxy'] = {
              k8s.restartDeployment("${variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['namespace']}-haproxy", variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['namespace'])
            }
          } else {
            for (def layer in serviceVars.defaultLayers) {
              def currentLayer = layer
              restarts[currentLayer] = {
                k8s.restartByLabel( [ "group": "${serviceVars.shortName}-${currentLayer}" ], variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['namespace'] )
              }
            }
          }

          parallel restarts
          k8s.clearAuth()
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
