@Library('sparrow-libraries@v0.3.2') _
variables = loadConstants()

import org.global.ParameterStore
import org.global.S3
import org.surveysparrow.SetupUtils

def setNotificationParams(config = [:]) {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${config.service} deployment",
      'TicketId': config.ticketId,
      'Deploying Branch': config.branchName,
      'Release Branch': config.releaseBranch,
      'Environment': config.releaseEnv,
      'Description': config.description,
      'Started By': env.BUILD_USER,
      'channel': config.channel
    ]
  }
}

def runMigrations(releaseBranch, releaseEnv, migrationType) {
  def logFile = "${migrationType}.txt"
  def s3Path = "migrationlogs/${releaseEnv}/${releaseBranch}/${logFile}"

  sh "NODE_ENV=${env.ENVIRONMENT} node server/migrate.js up --migration-type='${migrationType}' | tee ${logFile}"

  new S3(this).uploadFile("./${logFile}", variables.ARTIFACTS_BUCKET, s3Path)
  def s3Uri = "s3://${variables.ARTIFACTS_BUCKET}/${s3Path}"
  def migrationStatus = sh (returnStatus: true, script: "cat ${logFile} | grep 'RUNNING_MIGRATION_COMMAND_FAILED_WITH_ERRORS' && exit 1 || exit 0")
  sh "rm -f ${logFile}"

  return migrationStatus
}

def getJobParams() {
  def deploymentParams = [];

  if (env.JOB_NAME.contains('CommonPool')) {
    deploymentParams.add(
      choice(name: 'squad', choices: variables.SQUADS, description: 'Choose the squad name')
    )
  }

  deploymentParams = deploymentParams + [
    activeChoice(
      choiceType: 'PT_CHECKBOX',
      filterLength: 1,
      filterable: false,
      name: 'deploywithoutmerge',
      randomName: 'choice-parameter-1307506771402123',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return [\'Error\']'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return [\'Deploy without merge\']'
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'branchname',
      omitValueField: false,
      randomName: 'choice-parameter-1307506773037661',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''if (deploywithoutmerge.equals(\'Deploy without merge\')) {
            return """
            <div class=\'jenkins-form-description\'> Enter Squad / Release Branch Name</div><br>
            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
            """
            } else {
              return """
              <div class=\'jenkins-form-description\'> Enter Dev Branch Name </div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
              """
            }'''
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'description',
      omitValueField: false,
      randomName: 'choice-parameter-1307506775259089',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''return """
              <div class=\'jenkins-form-description\'>Enter description</div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
            """'''
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'ticketid',
      omitValueField: false,
      randomName: 'choice-parameter-1307506777573781',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''if (deploywithoutmerge.equals(\'Deploy without merge\')) {
              return "<br>"
              } else {
              return """
              <div class=\'jenkins-form-description\'>Enter Ticket ID</div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\'><br>
              """
            }'''
        ]
      )
    ),
    reactiveChoice(
      choiceType: 'PT_CHECKBOX',
      filterLength: 1,
      filterable: false,
      name: 'compileassets',
      randomName: 'choice-parameter-1307506780120040',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return [\'Error\']'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''if (deploywithoutmerge.equals(\'Deploy without merge\')) {
                return [\'Compile Assets\']
              } else { return [] }'''
        ]
      )
    )
  ]

  return deploymentParams
}

def getJobProperties() {
  def defaultProperties = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '5'))
  ]

  defaultProperties.add(parameters(getJobParams()))
  return defaultProperties
}

properties(getJobProperties())

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label 'DockerBuilderFleet'
  }

  tools {
    nodejs 'Node-v14.18.2'
  }

  environment {
    JOB_NAME    = "${env.JOB_NAME.split('/')[2]}"
    BB_TOKEN    = credentials('BitBucketOAuthToken')
    ARGOCD_CRED = credentials("${variables.ARGOCD.credentialId}")
  }

  stages {
    stage('VERIFY_TICKET_AND_BRANCH') {
      steps {
        script {
          service     = "App-v1"
          serviceVars = variables[service]
          releaseEnv  = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['env']
          setupUtils  = new SetupUtils(this, serviceVars, variables, releaseEnv)

          isDeployWithoutMerge = params.deploywithoutmerge.equals('Deploy without merge')
          branchName           = params.branchname.replace(',', '').trim()
          def description      = params.description.replace(',', '').trim()
          def ticketId         = (params.ticketid == null || params.ticketid == '') ? "DEPLOYED WITHOUT MERGE" : params.ticketid.replace(',', '').toUpperCase().trim()

          if (description == '') {
            wrap([$class: 'BuildUser']) {
              description = "$env.BUILD_USER, you have forgotten to add a description, please make sure to provide a description next time."
            }
          }

          if (isDeployWithoutMerge) {
            releaseBranch = branchName
            compileAssets = params.compileassets.equals('Compile Assets')
          } else {
            loadResourceFile('global/bitbuckethelper.py')
            releaseBranch = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-targetbranch -s ${branchName}", returnStdout: true).trim()
            prId          = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-prid -s ${branchName}", returnStdout: true).trim()
            def diffStat  = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-diff -p ${prId}", returnStdout: true).trim()
            def diffJson  = readJSON(text: diffStat)
            compileAssets = diffJson.new.any { it.startsWith('client/') } ||
                            diffJson.old.any { it.startsWith('client/') }
          }

          if ((variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch'] != null) && (!releaseBranch.startsWith(variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']))) {
            echo "[-] The branch should start with ${variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']}. Aborting deployment."
            error "[-] The branch should start with ${variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']}. Aborting deployment."
          }

          notificationConfig = [
            service: service, ticketId: ticketId,
            releaseBranch: releaseBranch, releaseEnv: releaseEnv,
            description: description, branchName: branchName
          ]

          if (params.squad) {
            def squadVariables = variables.SQUAD_BRANCH_ENV_MAP.find { squad, details -> squad.contains(params.squad) }?.value
            notificationConfig['channel'] = squadVariables.branch
          } else {
            notificationConfig['channel'] = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']
          }

          setNotificationParams(notificationConfig)
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${releaseBranch}"
          currentBuild.description = "${ticketId} - ${branchName} - ${description} - ${params.compileassets}"
        }
      }
    }

    stage('MERGE_PRS_UPDATE_SUBMODULES') {
      when {
        expression {
          return !isDeployWithoutMerge
        }
      }

      steps {
        script {
          for (def submodule in variables.SUBMODULES[serviceVars.service]) {
            def submodulePr = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} get-prid -s ${branchName}", returnStdout: true).trim()
            if (submodulePr) {
              def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} merge-prs -p ${submodulePr}", returnStatus: true)
              if (exitStatus != 0) {
                error "${submodule} PR merge failed."
              }
            }
          }

          def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} merge-prs -p ${prId}", returnStatus: true)
          if (exitStatus != 0) {
            error "${serviceVars.service} PR merge failed."
          }

          dir(serviceVars.folderName) {
            gitUtils.cloneRepoWithGit(releaseBranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              sh "git submodule update --init --recursive"

              for (submodule in variables.SUBMODULES[serviceVars.folderName]) {
                dir(submodule) {
                  if (sh (script: "git ls-remote | grep refs/heads/${releaseBranch}\$", returnStatus: true) == 0) {
                    sh """
                    git remote update
                    git fetch --all
                    git fetch origin ${releaseBranch}
                    """

                    try {
                      sh """
                      git checkout --track origin/${releaseBranch}
                      """
                    } catch (Exception e) {
                      echo "Error checking out ${releaseBranch} in ${submodule} - ${e}"
                      sh """
                      git checkout ${releaseBranch}
                      git pull
                      """
                    }
                  }
                }
              }

              gitUtils.setGitConfig()
              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                gitUtils.commitAndPush(releaseBranch, "Update submodule reference - merged ${branchName} to ${releaseBranch}")
              }
            }
          }
        }
      }
    }

    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          dir(serviceVars.folderName) {
            gitUtils.cloneRepo(releaseBranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            packageJsonHash = ifInstallNodeModules('package.json', variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}")
            def commitHash  = gitUtils.getCommitHash()
            buildTag        = "${commitHash}_${env.BUILD_NUMBER}"

            postMigrationExists = true // assigning true here, which will be overridden in PRE_MIGRATIONS stage
            if (compileAssets) {
              version = sh returnStdout: true, script: "echo ${commitHash}${releaseBranch} | md5sum - | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"
              version = "${version}_${env.BUILD_NUMBER}"
            } else {
              def versionParameter = "/${releaseEnv}/version"
              version = new ParameterStore(this).getParameter(versionParameter)
            }
          }

          dir (variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            configFilePath = "${releaseEnv}/${serviceVars.shortName}/${env.ENVIRONMENT}.json"
            // Since jq doesn't support in-place updates
            sh """
              jq '.version |= "${version}"' ${configFilePath} > temp.json
              rm ${configFilePath}
              mv temp.json ${configFilePath}
              cp ${configFilePath} ../${serviceVars.folderName}/config/${env.ENVIRONMENT}.json
              cp ${releaseEnv}/files/google/* ../${serviceVars.folderName}/config/
              cp ${releaseEnv}/files/cassandra/sf-class2-root.crt ../${serviceVars.folderName}/server/external/cassandra/sf-class2-root.crt
            """
          }
        }
      }
    }

    stage('BUILD') {
      parallel {
        stage('WEBPACK_ADMIN_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return compileAssets }
          }

          steps {
            script {
              setupUtils.prepareRepo(releaseBranch, version, packageJsonHash)

              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npm run prod-build:webpack-admin-app
                NODE_ENV=${env.ENVIRONMENT} npm run publish
                """
              }
            }
          }
        }

        stage('WEBPACK_EUI_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return compileAssets }
          }

          steps {
            script {
              setupUtils.prepareRepo(releaseBranch, version, packageJsonHash)
              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p prod-build:webpack-classic-form prod-build:rollup-reputation-embed prod-build:sass
                NODE_ENV=${env.ENVIRONMENT} npm run publish
                """
              }
            }
          }
        }

        stage('WEBPACK_GENERAL_BUILD') {
          agent { label 'WebpackBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          when {
            beforeAgent true
            expression { return compileAssets }
          }

          steps {
            script {
              setupUtils.prepareRepo(releaseBranch, version, packageJsonHash)
              dir(serviceVars.folderName) {
                sh """
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p prod-build:webpack prod-build:webpack-widget
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all -p dev-build:vendor-js prod-build:webpack-track_cloudfront_id_sw -s copy:*
                NODE_ENV=${env.ENVIRONMENT} npx npm-run-all publish*
                """
              }
            }
          }
        }

        stage('RUN_PRE_MIGRATIONS') {
          steps {
            script {
              dir (serviceVars.folderName) {
                def migrationStatus = runMigrations(releaseBranch, releaseEnv, 'PRE')
                if (migrationStatus != 0) {
                  error "[-] PRE migrations failed"
                }

                postMigrationExists = sh (returnStatus: true, script: "NODE_ENV=${env.ENVIRONMENT} node server/migrate.js pending --migration-type='POST'")
                postMigrationExists = (postMigrationExists == '1' || postMigrationExists == 1) ? false : true
              }
            }
          }
        }

        stage('DOCKER_BUILD') {
          agent { label 'DockerBuilderFleet' }
          tools { nodejs "Node-v14.18.2" }
          steps {
            script {
              setupUtils.prepareRepo(releaseBranch, version, packageJsonHash)
              def dockerRegistry = variables.APP_DOCKER_REGISTRY[variables.DEFAULT_REGION]
              def repository     = releaseEnv
              def dockerRepo     = dockerRegistry + "/" + repository
              def tags           = [ 'latest', buildTag ]

              dir(serviceVars.folderName) {
                sh "rm -rf node_modules/"
                dockerUtils.authenticate(dockerRegistry, variables.DEFAULT_REGION)
                dockerUtils.build("Dockerfiles/${env.ENVIRONMENT}", dockerRepo, tags)
                dockerUtils.push(dockerRepo, tags)
              }
            }
          }
        }
      }
    }

    stage('UPDATE_CONFIG_AND_CONTEXT') {
      steps {
        script {
          dir (variables.CONFIG_REPO.folder) {
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Asset version updated ${releaseEnv} - ${version}", configFilePath)
            }
          }

          dir (variables.KUBERNETES_REPO_NEW.folder) {
            gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO_NEW.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            def serviceYamlFile = "${env.ORG}/app-v1/kustomize/overlays/squad-env/${releaseEnv}/${serviceVars.shortName}/kustomization.yaml"

            def yamlData = readYaml(file: serviceYamlFile)
            yamlData.images.each { image -> image.newTag = buildTag }

            writeYaml file: serviceYamlFile, overwrite: true, data: yamlData

            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Image tag updated ${serviceVars.service} - ${releaseEnv} - ${buildTag}", serviceYamlFile)
            }
          }
        }
      }
    }

    stage('DEPLOY_TO_EKS') {
      steps {
        script {
          argocd (
            action: variables.ARGOCD.ACTIONS.deploy,
            argoCdUsername: ARGOCD_CRED_USR,
            argoCdPassword: ARGOCD_CRED_PSW,
            argoCdAppName: variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['appName'],
            argoCdDomain: variables.ARGOCD.domain
          )
        }
      }
    }

    stage('UPDATE_PARAMETER_STORE') {
      when {
        expression { return compileAssets }
      }

      steps {
        script {
          def parameterPath = "/${releaseEnv}/version"
          new ParameterStore(this).putParameter(parameterPath, version)
        }
      }
    }

    stage('RUNNING_POST_MIGRATIONS') {
      when {
        expression {
          return postMigrationExists
        }
      }

      steps {
        script {
          dir (serviceVars.folderName) {
            def migrationStatus = runMigrations(releaseBranch, releaseEnv, 'POST')
            if (migrationStatus != 0) {
              error "[-] POST migrations failed"
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
