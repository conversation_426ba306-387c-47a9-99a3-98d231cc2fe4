@Library('sparrow-libraries@v0.3.2') _
variables = loadConstants()

import org.global.ParameterStore
import org.global.S3

def setNotificationParams(config = [:]) {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${config.service} ${params.deployment} deployment",
      'Deploying Branch': config.branchName,
      'Release Branch': config.releaseBranch,
      'Environment': config.releaseEnv,
      'Description': config.description,
      'Started By': env.BUILD_USER,
      'channel': config.channel
    ]
  }
}

def runMigrations(releaseBranch, releaseEnv, migrationType) {
  def logFile = "${migrationType}.txt"
  def s3Path = "migrationlogs/${releaseEnv}/${params.branchname}/${migrationType}.txt"

  sh "NODE_ENV=${env.ENVIRONMENT} node migrate.js up --migration-type='${migrationType}' | tee ${logFile}"

  new S3(this).uploadFile("./${logFile}", variables.ARTIFACTS_BUCKET, s3Path)
  def s3Uri = "s3://${variables.ARTIFACTS_BUCKET}/${s3Path}"
  def migrationStatus = sh (returnStatus: true, script: "cat ${logFile} | grep 'RUNNING_MIGRATION_COMMAND_FAILED_WITH_ERRORS' && exit 1 || exit 0")
  sh "rm -f ${logFile}"

  return migrationStatus
}

def getJobParams() {
  def deploymentParams = []

  if (env.JOB_NAME.contains('CommonPool')) {
    deploymentParams.add(
      choice(name: 'squad', choices: variables.SQUADS, description: 'Choose the squad name')
    )
  }

  deploymentParams = deploymentParams + [
    activeChoice(
      choiceType: 'PT_CHECKBOX',
      filterLength: 1,
      filterable: false,
      name: 'deploywithoutmerge',
      randomName: 'choice-parameter-1307506771402123',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return [\'Error\']'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return [\'Deploy without merge\']'
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'branchname',
      omitValueField: false,
      randomName: 'choice-parameter-1307506773037661',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''if (deploywithoutmerge.equals(\'Deploy without merge\')) {
            return """
            <div class=\'jenkins-form-description\'> Enter Squad / Release Branch Name</div><br>
            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
            """
            } else {
              return """
              <div class=\'jenkins-form-description\'> Enter Dev Branch Name </div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
              """
            }'''
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'description',
      omitValueField: false,
      randomName: 'choice-parameter-1307506775259089',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''return """
              <div class=\'jenkins-form-description\'>Enter description</div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
            """'''
        ]
      )
    ),
    activeChoiceHtml(
      choiceType: 'ET_FORMATTED_HTML',
      name: 'ticketid',
      omitValueField: false,
      randomName: 'choice-parameter-1307506777573781',
      referencedParameters: 'deploywithoutmerge',
      script: groovyScript(
        fallbackScript: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: 'return "<h1>ERROR</h1>"'
        ],
        script: [
          classpath: [],
          oldScript: '',
          sandbox: true,
          script: '''if (deploywithoutmerge.equals(\'Deploy without merge\')) {
              return "<br>"
              } else {
              return """
              <div class=\'jenkins-form-description\'>Enter Ticket ID</div><br>
              <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\'><br>
              """
            }'''
        ]
      )
    ),
    choice (
      choices: variables.DEPLOYED_SOCS,
      description: 'Select the microservice to deploy',
      name: 'microservice'
    ),
    choice (
      choices: ['backend', 'frontend'],
      description: 'Select the service to deploy',
      name: 'deployment'
    )
  ]

  return deploymentParams
}

def getJobProperties() {
  def defaultProps = [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '2'))
  ]

  defaultProps.add(parameters(getJobParams()))
  return defaultProps
}

properties(
  getJobProperties()
)

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label 'DockerBuilderFleet'
  }

  tools {
    nodejs 'Node-v18.20.2'
  }

  environment {
    JOB_NAME    = "${env.JOB_NAME.split('/')[2]}"
    BB_TOKEN    = credentials('BitBucketOAuthToken')
    ARGOCD_CRED = credentials("${variables.ARGOCD.credentialId}")
  }

  stages {
    stage('VERIFY_TICKET_AND_BRANCH') {
      steps {
        script {
          isDeployWithoutMerge = params.deploywithoutmerge.equals('Deploy without merge')
          branchName           = params.branchname.replace(',', '').trim()
          def description      = params.description.replace(',', '').trim()
          def ticketId         = (params.ticketid == null || params.ticketid == '') ? "DEPLOYED WITHOUT MERGE" : params.ticketid.replace(',', '').toUpperCase().trim()

          if (description == '') {
            wrap([$class: 'BuildUser']) {
              description = "$env.BUILD_USER, you have forgot to add the description. Please provide the description next time."
            }
          }

          service     = params.microservice
          serviceVars = variables[service][params.deployment]

          if (!serviceVars) { error "[-] ${params.deployment} for ${params.microservice} is not yet released." }

          if (isDeployWithoutMerge) {
            releaseBranch = branchName
          } else {
            loadResourceFile('global/bitbuckethelper.py')
            releaseBranch = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-targetbranch -s ${branchName}", returnStdout: true).trim()
            prId          = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} get-prid -s ${branchName}", returnStdout: true).trim()
          }

          releaseEnv = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['env']

          currentBuild.displayName = "${service} - ${params.deployment}"
          currentBuild.description = "${releaseBranch} - ${ticketId}"

          def notificationConfig = [
            service: service, ticketId: ticketId, description: description,
            releaseEnv: releaseEnv, releaseBranch: releaseBranch, branchName: branchName
          ]

          if (params.squad) {
            def squadVariables = variables.SQUAD_BRANCH_ENV_MAP.find { squad, details -> squad.contains(params.squad) }?.value
            notificationConfig['channel'] = squadVariables.branch
          } else {
            notificationConfig['channel'] = variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['branch']
          }

          setNotificationParams(notificationConfig)
          notify("STARTED", notificationParams, variables)
        }
      }
    }

    stage('MERGE_PRS_UPDATE_SUBMODULES') {
      when {
        expression {
          return !isDeployWithoutMerge
        }
      }

      steps {
        script {
          for (def submodule in variables.SUBMODULES[serviceVars.service]) {
            def submodulePr = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} get-prid -s ${branchName}", returnStdout: true).trim()
            if (submodulePr) {
              def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${submodule} merge-prs -p ${submodulePr}", returnStatus: true)
              if (exitStatus != 0) {
                error "${submodule} PR merge failed."
              }
            }
          }

          def exitStatus = sh (script: "BB_TOKEN=$BB_TOKEN WORKSPACE=${variables.BITBUCKET_WORKSPACE} python3 bitbuckethelper.py -r ${serviceVars.service} merge-prs -p ${prId}", returnStatus: true)
          if (exitStatus != 0) {
            error "${serviceVars.service} PR merge failed."
          }

          dir(serviceVars.folderName) {
            gitUtils.cloneRepoWithGit(releaseBranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              sh "git submodule update --init --recursive"

              for (submodule in variables.SUBMODULES[serviceVars.folderName]) {
                dir(submodule) {
                  if (sh (script: "git ls-remote | grep refs/heads/${releaseBranch}\$", returnStatus: true) == 0) {
                    sh """
                    git remote update
                    git fetch --all
                    git fetch origin ${releaseBranch}
                    """

                    try {
                      sh """
                      git checkout --track origin/${releaseBranch}
                      """
                    } catch (Exception e) {
                      echo "Error checking out ${releaseBranch} in ${submodule} - ${e}"
                      sh """
                      git checkout ${releaseBranch}
                      git pull
                      """
                    }
                  }
                }
              }

              gitUtils.setGitConfig()
              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                gitUtils.commitAndPush(releaseBranch, "Update submodule reference - merged ${branchName} to ${releaseBranch}")
              }
            }
          }
        }
      }
    }

    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          dir(serviceVars.folderName) {
            gitUtils.cloneRepo(releaseBranch, serviceVars.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            packageJsonHash = ifInstallNodeModules('package.json', variables.ARTIFACTS_BUCKET, "node_modules/${env.JOB_NAME}")
            commitHash      = gitUtils.getCommitHash()
            buildTag        = "${commitHash}_${env.BUILD_NUMBER}"
          }

          if (params.deployment == 'backend') {
            def versionParameter = "/${releaseEnv}/version"
            version = new ParameterStore(this).getParameter(versionParameter)
            postMigrationExists = true
          } else {
            version = sh returnStdout: true, script: "echo ${commitHash}${releaseBranch} | md5sum - | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"
            version = "${version}_${env.BUILD_NUMBER}"
          }

          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            configFilePath = params.deployment == 'backend'
                          ? "${releaseEnv}/${serviceVars.shortName}/${env.ENVIRONMENT}.json"
                          : "${releaseEnv}/${serviceVars.backendDeployment}/${env.ENVIRONMENT}.json"

            sh """
              jq '.version |= \"${version}\"' ${configFilePath} > temp.json
              cp temp.json ../${serviceVars.folderName}/config/${env.ENVIRONMENT}.json
              mv temp.json ${configFilePath}
            """
          }
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          dir(serviceVars.folderName) {
            if (params.deployment == 'backend') {
              sh "mkdir -p db/credentials/cassandra/"
              sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO.folder}/${releaseEnv}/files/cassandra/ca.pem db/credentials/cassandra/"
              sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO.folder}/${releaseEnv}/files/cassandra/sf-class2-root.crt db/credentials/cassandra/"
              sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO.folder}/${releaseEnv}/files/google/* config/"

              def dockerRegistry = variables.APP_DOCKER_REGISTRY[variables.DEFAULT_REGION]
              def repository     = "${releaseEnv}-${serviceVars.shortName}"
              def dockerRepo     = dockerRegistry + "/" + repository
              def tags           = [ 'latest', buildTag ]

              // need to add node_modules to .dockerignore
              dockerUtils.authenticate(dockerRegistry, variables.DEFAULT_REGION)
              dockerUtils.build("Dockerfiles/${env.ENVIRONMENT}", dockerRepo, tags)
              dockerUtils.push(dockerRepo, tags)
            } else if (params.deployment == 'frontend') {
              sh "NODE_ENV=${env.ENVIRONMENT} npm run prod"
            } else {
              error "[-] Invalid Deployment"
            }
          }
        }
      }
    }

    stage ('RUNNING_PRE_MIGRATIONS') {
      when {
        expression { serviceVars.migrationsEnabled && params.deployment == 'backend' }
      }

      steps {
        script {
          dir (serviceVars.folderName) {
            def preMigrationStatus = runMigrations(releaseBranch, releaseEnv, 'PRE')
            if (preMigrationStatus != 0) {
              error "[-] PRE migrations failed."
            }

            postMigrationExists = sh (returnStatus: true, script: "NODE_ENV=${env.ENVIRONMENT} node migrate.js pending --migration-type='POST'")
            postMigrationExists = (postMigrationExists == '1' || postMigrationExists == 1) ? false : true
          }
        }
      }
    }

    stage ('UPDATE_CONFIG_AND_CONTEXT') {
      steps {
        script {
          dir(variables.CONFIG_REPO.folder) {
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              if (params.deployment == 'backend') {
                sh "git status"
                gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Asset version updated ${serviceVars.service} - ${releaseEnv} - ${version}", configFilePath)
              } else if (params.deployment == 'frontend') {
                def kustomizationPath = "${releaseEnv}/${serviceVars.backendDeployment}/kustomization.yaml"

                def yamlData = readYaml(file: kustomizationPath)
                yamlData.configMapGenerator.find { it.name == serviceVars.cmName }.literals[0] = "${serviceVars.cmKey}=${version}"

                writeYaml file: kustomizationPath, overwrite: true, data: yamlData

                sh "git status"
                gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Asset version updated ${serviceVars.service} - ${releaseEnv} - ${version}", kustomizationPath)
              } else {
                error "[-] Invalid deployment"
              }
            }
          }

          if (params.deployment == 'backend') {
            dir (variables.KUBERNETES_REPO_NEW.folder) {
              gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO_NEW.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)

              def serviceYamlFile = "${env.ORG}/app-v1/kustomize/overlays/squad-env/${releaseEnv}/${serviceVars.shortName}/kustomization.yaml"

              def yamlData = readYaml(file: serviceYamlFile)
              yamlData.images.each { image -> image.newTag = buildTag }

              writeYaml file: serviceYamlFile, overwrite: true, data: yamlData

              sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
                sh "git status"
                gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Image tag updated ${serviceVars.service} - ${releaseEnv} - ${buildTag}", serviceYamlFile)
              }

            }
          }
        }
      }
    }

    stage ('DEPLOY_OR_VALIDATE') {
      steps {
        script {
          argocd (
            action: variables.ARGOCD.ACTIONS.deploy,
            argoCdUsername: ARGOCD_CRED_USR,
            argoCdPassword: ARGOCD_CRED_PSW,
            argoCdAppName: variables.SQUAD_BRANCH_ENV_MAP[env.JOB_NAME]['appName'],
            argoCdDomain: variables.ARGOCD.domain,
          )

          if (params.deployment == 'frontend') {
            k8s.authenticate(variables.CLUSTER_REGION_MAP[variables.DEFAULT_REGION], variables.DEFAULT_REGION)
            def podsFromCmd = sh (returnStdout: true, script: "kubectl get pods -n ${releaseEnv} | grep ${releaseEnv}-${serviceVars.backendDeployment}-application")
            def pods        = podsFromCmd.split('\n')

            for (pod in pods) {
              if (pod.contains("Running")) {
                def podHostName = pod.split(" ")[0]
                def podIp       = sh (returnStdout: true, script: "kubectl get pod ${podHostName} -n ${releaseEnv} -o=jsonpath='{.status.podIP}'").trim()
                def podVersion = sh (returnStdout: true, script: "curl -X POST -s ${podIp}:8080${serviceVars.validationPath} | jq -r '.${serviceVars.versionKey}'").trim()
                if (podVersion != version) {
                  sh "kubectl delete pod ${podHostName} -n ${releaseEnv}"
                }
              }
            }
          }
        }
      }
    }

    stage ('UPDATE_PARAMETER_STORE') {
      when {
        expression { params.deployment == 'frontend' }
      }

      steps {
        script {
          def parameterPath = "/${releaseEnv}/${service}/version"
          new ParameterStore(this).putParameter(parameterPath, version)
        }
      }
    }

    stage ('RUNNING_POST_MIGRATIONS') {
      when {
        expression { serviceVars.migrationsEnabled && postMigrationExists && params.deployment == 'backend' }
      }

      steps {
        script {
          dir(serviceVars.folderName) {
            def postMigrationStatus = runMigrations(releaseBranch, releaseEnv, 'POST')
            if (postMigrationStatus != 0) {
              error "[-] POST migrations failed."
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
