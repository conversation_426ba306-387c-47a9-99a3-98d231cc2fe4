@Library('sparrow-libraries@master') _ // Adding master here until the changelog pipeline is fixed.
variables = loadConstants()

import org.global.ParameterStore

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} scale deployment",
      'Region': params.region,
      'Layer': params.layer,
      'Config': "Min: ${params.minreplicas}, Max: ${params.maxreplicas}",
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice (name: 'region', choices: variables.REGION_NAMES, description: 'Specify which region to run scale deployment'),
    choice (name: 'layer', choices: variables.LAYERS.application + variables.LAYERS.worker, description: 'Specify which layer to scale the deployment'),
    string (name: 'minreplicas', defaultValue: '2', description: 'Specify the minimum number of replicas'),
    string (name: 'maxreplicas', defaultValue: '10', description: 'Specify the maximum number of replicas'),
    string (name: 'reason', description: 'Specify the reason to scale the deployment')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else {
            service = serviceIdentifier[1]
            serviceVars = variables[service]['backend']
          }

          region     = variables.DATA_CENTER_REGION_MAP[params.region]
          releaseEnv = variables.REGION_APP_MAP[region] // used only for staging

          if (!serviceVars.dcLayerMap[region]) {
            currentBuild.description = "${params.region} not present for service - ${service}"
            error "[-] Region - ${params.region} not present for service - ${service}"
          }

          if (
            serviceVars.dcLayerMap[region].workerLayers.contains(params.layer) ||
            serviceVars.dcLayerMap[region].appLayers.contains(params.layer)
          ) {
            currentBuild.displayName = "${params.region}"
            currentBuild.description = "${params.layer} - min: ${params.minreplicas}, max: ${params.maxreplicas}"

            appName = variables.REGION_APP_MAP[region]
          } else {
            error "[-] Layer - ${params.layer} not present for service - ${service} in region - ${params.region}"
          }

          setNotificationParams()
          notify('STARTED', notificationParams, variables)
        }
      }
    }

    stage('SCALE_DEPLOYMENT') {
      steps {
        script {
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          if (serviceVars.service == 'app-v1') {
            def blueGreenParams = ['blue', 'green']

            for (def param in blueGreenParams) {
              def deploymentParameter = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/deployment/${param}" : "/${region}/deployment/${param}"
              def deploymentParam     = new ParameterStore(this).getParameter(deploymentParameter)

              def hpaName = "${appName}-${params.layer}-${deploymentParam}-hpa"
              def patch   = "{\"spec\":{\"minReplicas\":${params.minreplicas},\"maxReplicas\":${params.maxreplicas}}}"

              k8s.patchResource('hpa', hpaName, patch, serviceVars.namespace)
            }
          } else {
            def hpaName = "${appName}-${serviceVars.shortName}-${params.layer}-hpa"
            def patch   = "{\"spec\":{\"minReplicas\":${params.minreplicas},\"maxReplicas\":${params.maxreplicas}}}"

            k8s.patchResource('hpa', hpaName, patch, serviceVars.namespace)
          }
        }
      }
    }

    stage('COMMIT_CHANGES') {
      steps {
        script {
          dir(variables.KUBERNETES_REPO.folder) {
            gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, variables.KUBERNETES_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def yamlBasePath = env.ENVIRONMENT == 'staging' ? "${variables.KUBERNETES_REPO.folder}/kubectl/preproduction/${region}/" : "${variables.KUBERNETES_REPO.folder}/app-v1/kubectl/${region}/"
          def yamlFolder   = serviceVars.service == 'app-v1' ? "blue-green-templates/" : "${serviceVars.shortName}/"

          if (serviceVars.dcLayerMap[region].workerLayers.contains(params.layer)) {
            fileName = "worker-hpa.yaml"
          }

          if (serviceVars.dcLayerMap[region].appLayers.contains(params.layer)) {
            fileName = "app-hpa.yaml"
          }

          def yamlFile = "${yamlBasePath}${yamlFolder}${fileName}"

          def hpaNamePrefix = serviceVars.service == 'app-v1' ? "${appName}-${params.layer}-" : "${appName}-${serviceVars.shortName}-${params.layer}-hpa"
          def hpaYaml      = readYaml file: yamlFile
          def documents    = hpaYaml instanceof List ? hpaYaml : [hpaYaml]

          documents.collect { hpa ->
            if (hpa.metadata.name.startsWith(hpaNamePrefix)) {
              hpa.spec.minReplicas = params.minreplicas.toInteger()
              hpa.spec.maxReplicas = params.maxreplicas.toInteger()
            }

            return hpa
          }

          def updatedYaml = documents.collect { writeYaml returnText: true, data: it }.join("\n---\n")
          writeFile file: 'updated-deployment.yaml', text: updatedYaml

          sh """
          rm -f ${yamlFile}
          cp updated-deployment.yaml ${yamlFile}
          """

          dir (variables.KUBERNETES_REPO.folder) {
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              gitUtils.commitAndPush(variables.DEFAULT_BRANCH, "Scaled layer: ${params.layer} - ${serviceVars.shortName} - ${params.reason}, min: ${params.minreplicas}, max: ${params.maxreplicas}")
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
