@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

def setNotificationParams(config = [:]) {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} ${customConfig} config update",
      'Region': params.region,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice(name: 'region', choices: variables.REGION_NAMES, description: 'Specify the region to update the config'),
    string(name: 'reason', description: 'Specify the reason to update the config')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else {
            service     = serviceIdentifier[1]
            serviceVars = variables[service]['backend']
          }

          customConfig = env.JOB_BASE_NAME.toLowerCase()
          region      = variables.DATA_CENTER_REGION_MAP[params.region]
          releaseEnv  = variables.REGION_APP_MAP[region]
          setNotificationParams()
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.reason}"
        }
      }
    }

    stage('UPDATE_CONFIG') {
      steps {
        script {
          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def configFileDir = "${variables.CONFIG_REPO.folder}/${params.region}/${serviceVars.shortName}/${customConfig}/"
          loadResourceFile('surveysparrow/cmupdater.py')

          sh "python3 cmupdater.py -c ${serviceVars.shortName}-${customConfig}-config -n ${serviceVars.namespace} -d ${configFileDir} -e ${configFileDir}kustomization.yaml"
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          k8s.apply('./cm-template.yaml')
        }
      }
    }

    stage('RESTART_CONTAINERS') {
      steps {
        script {
          k8s.restartByLabel([ "${customConfig}": "${serviceVars.shortName}" ], serviceVars.namespace)
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
