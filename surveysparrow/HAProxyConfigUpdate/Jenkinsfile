@Library('sparrow-libraries@v0.2.0') _
variables = loadConstants()

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} HAProxy config update",
      'Region': params.region,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice(name: 'region', choices: variables.REGION_NAMES, description: 'Specify the region to update the haproxy configuration'),
    string(name: 'reason', description: 'Specify the reason to update the haproxy configuration')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else {
            error "HAProxy updates should be done from App-v1"
          }

          region     = variables.DATA_CENTER_REGION_MAP[params.region]
          setNotificationParams()
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.reason}"
        }
      }
    }

    stage('UPDATE_CONFIG') {
      steps {
        script {
          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          def configFileDir = "${variables.CONFIG_REPO.folder}/${params.region}/haproxy/"
          loadResourceFile('surveysparrow/cmupdater.py')

          // harcoding green right now - haproxy will be combined to single even if we get to bluegreen
          sh "python3 cmupdater.py -c haproxy-config-green -n ${serviceVars.namespace} -d ${configFileDir} -e ${configFileDir}kustomization.yaml"
          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          k8s.apply('./cm-template.yaml')
        }
      }
    }

    stage('RESTART_CONTAINERS') {
      steps {
        script {
          def deploymentName = env.ENVIRONMENT == 'staging' ? 'haproxy' : 'haproxy-forwarder'
          k8s.restartDeployment(deploymentName, serviceVars.namespace)
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
