# NPM Package Deployment Pipeline

This Jenkins pipeline automates the process of building, versioning, and publishing NPM packages for the Surveysparrow project. It is designed to work with Jenkins and leverages shared libraries and environment-specific configurations.

## Pipeline Overview

The pipeline performs the following steps:

1. **Initialize Environment**
   - Clones the package repository from Bitbucket.
   - Sets up the working directory and determines the NPM tag based on the environment.

2. **Setup NPM Auth**
   - Validates the package name in `package.json`.
   - Configures the NPM authentication token for publishing.

3. **Install Dependencies**
   - Installs package dependencies using `npm ci` for a clean and reproducible build.

4. **Publish Package**
   - Bumps the package version according to the selected patch type (`patch`, `minor`, or `major`).
   - Publishes the package to the NPM registry with the appropriate access and tag.
   - Commits and pushes the version bump and tags to the repository.

5. **Post Actions**
   - Cleans up the NPM authentication token.
   - Sends notifications to the configured Slack channel.
   - Cleans the workspace.

## Parameters

- **patchtype**: Select the type of version bump (`patch`, `minor`, `major`).
- **tag**: (Staging only) Select the NPM tag to publish to (`beta`, `alpha`, `dev`, `staging`, `stable`, `canary`, `next`).

## Notifications

- Sends build and publish notifications to the `#npm-packages` Slack channel, including the user who triggered the build and a link to the build logs.

## Environment

- Runs on agents labeled `DockerBuilderFleet`.
- Uses the Node.js version specified in the package configuration (defaults to Node v20.18.0).
- Requires the `NpmToken` credential for publishing to NPM.
- Uses Bitbucket credentials for repository operations.

## Error Handling

- The pipeline will fail and send notifications if any of the following occur:
  - Repository cloning fails.
  - Package name mismatch in `package.json`.
  - Dependency installation fails.
  - Publishing to NPM fails.
  - Git tagging or pushing fails.

## Customization

- Package-specific configurations (repository URL, NPM scope, sub-path, etc.) are loaded from a shared constants library.
- The pipeline supports both public and restricted NPM scopes.

### Example `NPM_PACKAGES` Groovy Map Configuration

Below is an example of how the `NPM_PACKAGES` map might be defined in your shared Groovy constants:

```groovy
[
   'NPM_PACKAGES' = [
      'PackageA': [
         packageName: 'my-npm-package-a',
         repoUrl: '*****************:myorg/my-npm-package-a.git',
         npmScope: 'public', // or 'restricted'
         subPath: '',        // optional, if the package is in a subdirectory
         nodeVersion: 'Node-v20.18.3' // optional, defaults if not specified
      ]
   ]
]
```

## Usage

This pipeline is intended to be triggered via Jenkins, either manually or as part of an automated process. Select the desired patch type (and tag, if applicable) when starting the build.
