@Library('sparrow-libraries@master') _
variables = loadConstants()

appName = env.JOB_BASE_NAME
appConfig = variables.NPM_PACKAGES[appName]

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "Package Deployment: `${appConfig.packageName}`",
      'channel': '#npm-packages',
      'Published By': env.BUILD_USER,
      'logs': "\n<${env.BUILD_URL}|Click here for more details>"
    ]
  }
}

List<Object> getParameters() {
  List<Object> params = [
    choice(
      name: 'patchtype',
      choices: ['patch', 'minor', 'major'],
      description: 'Select the patch type to publish to'
    )
  ]

  if (env.ENVIRONMENT == 'staging') {
    params.add(
      choice(
        name: 'tag',
        choices: ['beta', 'alpha', 'dev', 'staging', 'stable', 'canary', 'next'],
        description: 'Select the tag to publish to'
      )
    )
  }

  return params
}

properties([
  disableConcurrentBuilds(),
  buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5')),
  parameters(getParameters())
])

pipeline {
  agent {
    label 'DockerBuilderFleet'
  }

  environment {
    NPM_TOKEN = credentials('NpmToken')
  }

  tools {
    nodejs appConfig.nodeVersion ?: 'Node-v20.18.3'
  }

  stages {
    stage('Initialize Environment') {
      steps {
        script {
          setNotificationParams()
          dir('package') {
            try {
              gitUtils.cloneRepoWithGit(variables.DEFAULT_BRANCH, appConfig.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
            } catch (e) {
              notificationParams['Error Message'] = 'Failed to clone repository'
              error "${notificationParams['Error Message']} ${e}"
            }
          }
          path = appConfig.subPath ? "package/${appConfig.subPath}" : 'package'
          tag = env.ENVIRONMENT == 'staging' ? params.tag : 'latest'
          notificationParams['Tag'] = tag
        }
      }
    }

    stage('Setup NPM Auth') {
      steps {
        script {
          dir(path) {
            try {
              sh "jq -e '.name == \"${appConfig.packageName}\"' package.json"
            } catch (e) {
              notificationParams['Error Message'] = 'Package name mismatch in package.json'
              error "${notificationParams['Error Message']} ${e}"
            }
            sh 'npm config set //registry.npmjs.org/:_authToken=${NPM_TOKEN}'
          }
        }
      }
    }

    stage('Install Dependencies') {
      steps {
        script {
          dir(path) {
            try {
              sh 'npm ci'
            } catch (e) {
              notificationParams['Error Message'] = 'Failed to install dependencies'
              error "${notificationParams['Error Message']} ${e}"
            }
          }
        }
      }
    }

    stage('Publish Package') {
      steps {
        script {
          dir(path) {
            gitUtils.setGitConfig()
            String version = sh(script: "npm version ${params.patchtype}", returnStdout: true).trim()
            notificationParams['Version'] = "${version} (${params.patchtype})"
            
            String scope = appConfig.npmScope == 'public' ? 'public' : 'restricted'

            try {
              sh "npm publish --access=${scope} --tag=${tag}"
            } catch (e) {
              notificationParams['Error Message'] = 'Failed to publish package'
              error "${notificationParams['Error Message']} ${e}"
            }
            
            sshagent([variables.BITBUCKET_CREDENTIAL_ID]) {
              try {
                sh "git push origin ${variables.DEFAULT_BRANCH}"
                sh "git tag ${version} -f && git push origin ${version}"
                sh "git tag ${tag} -f && git push origin ${tag} -f"
              } catch (e) {
                notificationParams['Error Message'] = 'Failed to tag or push changes to git'
                error "${notificationParams['Error Message']} ${e}"
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        sh 'npm config delete //registry.npmjs.org/:_authToken'
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
