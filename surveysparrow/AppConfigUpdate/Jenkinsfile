@Library('sparrow-libraries@master') _
variables = loadConstants()

import org.global.ParameterStore

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': "${service} config update",
      'Region': params.region,
      'Reason': params.reason,
      'Started By': env.BUILD_USER,
      'channel': variables.DEFAULT_DEPLOYMENTS_CHANNEL
    ]
  }
}

def getJobParams() {
  return [
    choice(name: 'region', choices: variables.REGION_NAMES, description: 'Specify the region to update the config'),
    string(name: 'reason', description: 'Specify the reason to update the config')
  ]
}

def getProperties() {
  return defaultProperties = [
    parameters(getJobParams()),
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
  ]
}

properties(getProperties())

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  stages {
    stage('VALIDATE_INPUT') {
      steps {
        script {
          def serviceIdentifier = env.JOB_NAME.split('/')
          if (serviceIdentifier[0] == "App-v1") {
            service = "App-v1"
            serviceVars = variables[service]
          } else if (serviceIdentifier[0] == 'MicroServices') {
            service = serviceIdentifier[1]
            serviceVars = variables[service]['backend']
          }

          region     = variables.DATA_CENTER_REGION_MAP[params.region]
          releaseEnv = variables.REGION_APP_MAP[region]
          setNotificationParams()
          notify('STARTED', notificationParams, variables)

          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.reason}"
        }
      }
    }

    stage('UPDATE_CONFIG') {
      steps {
        script {
          dir(variables.CONFIG_REPO.folder) {
            gitUtils.cloneRepo(variables.PRODUCTION_BRANCH, variables.CONFIG_REPO.repoUrl, variables.BITBUCKET_CREDENTIAL_ID)
          }

          configFilePath = "${variables.CONFIG_REPO.folder}/${params.region}/${serviceVars.shortName}/${env.ENVIRONMENT}.json"

          def parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/version/green" : "/${region}/version/green"
          def version = new ParameterStore(this).getParameter(parameterPath)
          sh "jq '.version |= \"${version}\"' ${configFilePath} > temp.json"

          def configMapName = ""
          if (serviceVars.service == 'app-v1') {
            parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/configmap/green" : "/${region}/configmap/green"
            def configMap  = new ParameterStore(this).getParameter(parameterPath)
            configMapName = "${serviceVars.namespace}-config-${configMap}"
          } else {
            configMapName = "${serviceVars.namespace}-${serviceVars.shortName}-config"
          }

          k8s.authenticate(variables.CLUSTER_REGION_MAP[region], region)
          createAndApplyConfigMap (
            cmName: configMapName,
            namespace: serviceVars.namespace,
            data: [
              "${env.ENVIRONMENT}.json": readFile('temp.json')
            ]
          )
        }
      }
    }

    stage('RESTART_CONTAINERS') {
      steps {
        script {
          def appName   = variables.REGION_APP_MAP[region]
          def allLayers = serviceVars.dcLayerMap[region].appLayers + serviceVars.dcLayerMap[region].workerLayers

          def parameterPath = env.ENVIRONMENT == 'staging' ? "/${releaseEnv}/deployment/green" : "/${region}/deployment/green"
          def deployment    = new ParameterStore(this).getParameter(parameterPath)

          def rollouts = [:]
          for (def layer in allLayers) {
            def deploymentName = ""
            if (serviceVars.service == 'app-v1') {
              deploymentName = "${appName}-${layer}-${deployment}"
            } else {
              deploymentName = "${appName}-${serviceVars.shortName}-${layer}"
            }

            rollouts[deploymentName] = {
              k8s.restartDeployment(deploymentName, serviceVars.namespace)
            }
          }

          parallel rollouts
        }
      }
    }
  }

  post {
    always {
      script {
        notificationParams['logs'] = "<${env.BUILD_URL}console|Click here to check logs>"
        notify(currentBuild.result, notificationParams, variables)
        cleanWs()
      }
    }
  }
}
