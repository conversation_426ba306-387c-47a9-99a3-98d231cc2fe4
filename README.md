# Sparrow Pipeline Documentation

A centralized repository for managing pipelines for Sparrow's products and environments.

## Folder Structure

```plaintext
├── Product-Name-1
│   ├── service-a
│   │   ├── Jenkinsfile
│   ├── service-b
│   │   ├── Jenkinsfile
├── Product-Name-2
│   ├── service-x
│   │   ├── Jenkinsfile
│   └── service-y
│       ├── Jenkinsfile
└── README.md
```

## Branching Strategy

### Branches

#### `master`
- Represents the production environment.
- Only stable and verified changes are merged here.
- This is the main branch used for live deployments.

#### `preproduction-master`
- Serves as the staging branch for testing and validation before merging into `master`.
- All feature branches must be merged into `preproduction-master` for testing.

#### `release`
- Used for testing purposes before merging into `preproduction-master`.
- Acts as a pre-staging branch for validating changes.

#### `feature-<service-name>`
- Used for new deployments or enhancements related to specific services.
- Example: `feature-surveysparrow-api`.


### Process

#### Feature Development
1. Create a `feature-<service-name>` branch for the specific feature.
2. Develop and test changes in the feature branch.
3. Submit a Pull Request (PR) to merge into `release`.

#### Release Validation
1. All feature branches are merged into `release` for testing.
2. After successful validation, changes in `release` can be merged into `preproduction-master`.

#### Preproduction Validation
1. Changes in `preproduction-master` undergo further integration testing and validation.
2. Once verified, changes can be promoted to `master`.

#### Production Deployment
1. Once approved, merge `preproduction-master` into `master` for production deployment.


---


## Review Process
***For PR titles, let's use the following format: ```<Ticket-ID> <TypeNoun>: <Description>```***

***For example: ```SSOPS-1801 Feature: Add Prism Pipeline```.***

**Direct merges into the master branch are restricted**.
**A minimum of two approvals is required to merge a feature branch into the master branch. For merging a hotfix branch**

**one approval is required, and it must come from the default reviewer.**