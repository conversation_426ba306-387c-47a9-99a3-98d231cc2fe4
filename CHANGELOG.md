# [](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.5.3...v) (2025-07-09)



## [1.5.3](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.5.2...v1.5.3) (2025-07-07)



## [1.5.2](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.5.1...v1.5.2) (2025-07-04)



## [1.5.1](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.5.0...v1.5.1) (2025-06-16)


### Bug Fixes

* update Cloudflare initialization in Jenkinsfile to use envConfig for zoneId, ensuring correct configuration ([3fd9492](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/3fd9492bf16c855aeef1540477b3073838434519))



# [1.5.0](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.4.2...v1.5.0) (2025-06-12)


### Bug Fixes

* correct alias setup for pnpm in Jenkinsfile to ensure proper package management ([3d56feb](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/3d56feb2cb113aead169fad1c8906e739acdd26d))


### Features

* add corepack installation and alias setup for pnpm and yarn in Jenkinsfile to streamline package management ([f27586a](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/f27586a915b9c4e6c8132b0e180c5456ec12a70e))
* enhance package manager detection in Jenkinsfile to support pnpm and bun, improving installation and build commands ([4c5064b](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/4c5064b45078670ba7c0e5c51b93608052494f99))



## [1.4.2](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.4.1...v1.4.2) (2025-06-10)



## [1.4.1](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.4.0...v1.4.1) (2025-06-09)



# [1.4.0](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.3.2...v1.4.0) (2025-06-04)


### Features

* add git push command for default branch in Jenkinsfile to ensure branch updates during deployment ([b111fcf](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/b111fcff6dfe4a83de7fbe50644c4a5c3a5e5e05))



## [1.3.2](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.3.1...v1.3.2) (2025-06-03)


### Bug Fixes

* correct nodejs tool declaration in Jenkinsfile to remove unnecessary string interpolation ([1d17cb4](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/1d17cb46a2caced3f69cda1f814c09dc602257ed))
* remove forced push for version tag in Jenkinsfile to prevent potential data loss ([d6d4356](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/d6d4356662c83a333442ff4f7234b603fbc595d5))
* remove unnecessary blank line in Jenkinsfile to improve readability ([61112c5](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/61112c515ee937fadc8bba444ff5427139fb59ca))
* update Jenkinsfile to force tag creation and push for versioning consistency ([36bb721](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/36bb721bc705b18f62c00a18f065cf34becd0fdd))
* update nodejs tool declaration in Jenkinsfile to use string interpolation for improved clarity ([40ac3cc](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/40ac3cc1001e2388a663e7eb758ad3aef2c32ac6))



## [1.3.1](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.3.0...v1.3.1) (2025-06-02)



# [1.3.0](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.2.1...v1.3.0) (2025-06-02)


### Bug Fixes

* improve error handling in Jenkinsfile by standardizing notification parameter keys and enhancing error messages ([a54a061](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/a54a0614b90662855b44191b7e461824ae54f5a3))


### Features

* add Jenkinsfile for NPM package deployment with versioning and notification setup ([6022d6d](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/6022d6d47ea97bde87c48072249140a0061befcc))
* add logging details and dynamic tagging to notification parameters in Jenkinsfile for NPM package deployment ([3c52087](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/3c52087b15bc7494405ecf912ede9023e5568e09))
* add README for NPM package deployment pipeline detailing steps, parameters, and error handling ([9dca94d](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/9dca94dd02cb3161f3a0b4b5bd02c14ab02456ec))
* enhance Jenkinsfile for NPM package deployment with improved error handling and dynamic path configuration ([ac1f8b4](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/ac1f8b40985e92c872c0a45929fa9594c0c04023))
* enhance Jenkinsfile to support dynamic tagging for NPM package deployment based on environment ([14864b4](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/14864b4fa48770eb9721a4c0870cc4aef51d52f9))
* implement comprehensive Jenkins pipeline for frontend deployment, including stages for preparation, build, config map update, and validation ([a07de80](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/a07de808ffcc3f0db1df5e27f160311c6a5ebe4c))
* Jenkinsfile for NPM package deployment ([9af5e36](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/9af5e366dab85d65e31c1f3aa5ce7faa30cf4e8c))



## [1.2.1](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.2.0...v1.2.1) (2025-05-29)


### Bug Fixes

* refine environment variable handling in CloudflarePages Jenkinsfile to ensure proper parsing ([6df619b](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/6df619b55e518f4bda0554784e66d557b07a980f))



# [1.2.0](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/v1.1.0...v1.2.0) (2025-05-27)


### Bug Fixes

* improve error handling in post-deployment command of CloudflarePages Jenkinsfile ([23e5d71](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/23e5d71c4b28cffc59082f71fba0a9c0985270a8))


### Features

* add post-deployment stage to CloudflarePages Jenkinsfile and update sparrow-libraries version to 0.3.0 ([51e84b4](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/51e84b4929944a4aa4b8354c99e6f0e1a55ddaa9))



# [1.1.0](https://bitbucket.org/surveysparrow/sparrow-pipeline/compare/12fd1ec43d093fa3c1ef6f8411a8a169e2f58fe9...v1.1.0) (2025-05-05)


### Bug Fixes

* update purgeCache method to accept a list for domain parameter ([ee57482](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/ee57482c6b4a52a5dfa4e76bc6201f32f6f0a868))


### Features

* Jenkinsfile for Cloudflare Pages deployment pipeline ([12fd1ec](https://bitbucket.org/surveysparrow/sparrow-pipeline/commits/12fd1ec43d093fa3c1ef6f8411a8a169e2f58fe9))



