
@Library('sparrow-libraries@master') _
variables = loadConstants()

import org.global.Cloudflare

def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    notificationParams = [
      'pipeline': env.JOB_NAME.split('/').last(),
      'Domain': domains,
      'Paths': paths,
      'Environment': env.ENVIRONMENT,
      'Started By': env.BUILD_USER,
      'Description': jobDescription,
      'channel': variables.WEBSITE.websiteDeploymentsChannel,
      'logs': "\n<${env.BUILD_URL}|Click here for more details>"
    ]   
  }
}

properties([
  parameters([
    activeChoice(
      choiceType: 'PT_SINGLE_SELECT', 
      description: 'Type of cache to purge. hosts - Purge cache for the specified hostnames. [prefixes - Purge cache for the specified path prefixes] [files - Purge cache for the specified files]',
      filterLength: 1, 
      filterable: false, 
      name: 'cacheType', 
      randomName: 'choice-parameter-7404054524953575', 
      script: groovyScript(
        fallbackScript: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: 'return ["ERROR"]'
        ],
        script: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: 'return ["hosts","prefixes","files"]'
        ]
      )
    ),
    string(name: 'urlPath', description: 'Path to purge [include leading slash] (Note: This is required only if you have selected path prefix or full path as cache type)'),
    string(name: 'description', description: 'Mention the reason for this cache purge?'),
    activeChoice(
      choiceType: 'PT_SINGLE_SELECT', 
      description: 'Select the domain to purge the cache for',
      filterLength: 1, 
      filterable: false, 
      name: 'domain', 
      randomName: 'choice-parameter-7404054524952323', 
      script: groovyScript(
        fallbackScript: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: 'return ["ERROR"]'
        ],
        script: [
          classpath: [], 
          oldScript: '', 
          sandbox: true, 
          script: variables.CLOUDFLARE.baseDomain
        ]
      )
    )
  ])
])

pipeline {
  agent {
    label 'AlwaysOnFleet'
  }

  environment {
    CLOUDFLARE_API_TOKEN = credentials("CommonCloudflareToken")
  }

  stages {
    stage("INITIALIZE") {
      steps {
        script {

          zoneId           = variables.CLOUDFLARE.DOMAINS["${params.domain}"]["zoneId"]
          domains           = params.domain.split(",")
          jobDescription   = (params.description == '') ? 'No description provided' : params.description
          paths             = (params.cacheType == 'hosts') ? [''] : params.urlPath.split(" ") 

          domainsList = Arrays.asList(domains)
          pathsList = Arrays.asList(paths)

          setNotificationParams()
          notify( "STARTED", notificationParams, variables )
        }
      }
    }
    stage("PURGE_CACHE") {
      steps {
        script {
          cloudflare = new Cloudflare(this, zoneId, CLOUDFLARE_API_TOKEN)
          cloudflare.purgeCache(params.cacheType, domainsList, pathsList)

          echo "Waiting for the Cache to be purged"
          sh "sleep 30"
        }
      }
    }
  }
  post {
    always {
      script {
        notify( currentBuild.result, notificationParams, variables )

        currentBuild.displayName = "${currentBuild.result}: #${currentBuild.number}"
        currentBuild.description = "Cache Purge of Domain: ${params.domain}\n cacheType: ${params.cacheType}"
        cleanWs()
      }
    }
  }
}